{"name": "only-chat-be", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "dependencies": {"@apollo/server": "^4.12.0", "@nestjs-modules/ioredis": "^1.0.1", "@nestjs-modules/mailer": "^2.0.2", "@nestjs/apollo": "^13.1.0", "@nestjs/common": "^11.0.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.0.1", "@nestjs/event-emitter": "^2.0.4", "@nestjs/graphql": "^13.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.0.1", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.0.1", "@nestjs/platform-socket.io": "^11.0.1", "@nestjs/swagger": "^11.1.3", "@nestjs/websockets": "^11.0.1", "@socket.io/redis-adapter": "^8.2.1", "@types/bcrypt": "^5.0.2", "@types/cookie-parser": "^1.4.8", "@types/nodemailer": "^6.4.17", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-jwt": "^4.0.1", "apollo-server-express": "^3.13.0", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "bcrypt": "^5.1.1", "cache-manager": "^5.4.0", "cache-manager-redis-store": "^3.0.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "cookie-parser": "^1.4.7", "dataloader": "^2.2.3", "geoip-lite": "^1.4.10", "graphql": "^16.10.0", "graphql-relay": "^0.10.2", "graphql-subscriptions": "^3.0.0", "graphql-type-json": "^0.3.2", "graphql-ws": "^6.0.4", "i18n-iso-countries": "^7.14.0", "ioredis": "^5.3.2", "joi": "^17.13.3", "kafkajs": "^2.2.4", "lodash": "^4.17.21", "mongoose": "^8.13.2", "nestjs-i18n": "^10.5.1", "nodemailer": "^6.10.1", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-jwt": "^4.0.1", "randomstring": "^1.3.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.1", "socket.io": "^4.7.5", "swagger-ui-express": "^5.0.1", "useragent": "^2.3.0", "uuid": "^11.1.0", "ws": "^8.18.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.18.0", "@nestjs/cli": "^11.0.0", "@nestjs/schematics": "^11.0.0", "@nestjs/testing": "^11.0.1", "@swc/cli": "^0.6.0", "@swc/core": "^1.10.7", "@types/express": "^5.0.0", "@types/graphql-relay": "^0.7.0", "@types/jest": "^29.5.14", "@types/multer": "^1.4.12", "@types/node": "^22.10.7", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.2", "globals": "^16.0.0", "jest": "^29.7.0", "prettier": "^3.4.2", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.2.5", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.3", "typescript-eslint": "^8.20.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}