#!/bin/bash

# Simple deployment script for Only Chat Backend

# Configuration
SERVER="root@**************"
ROOT_PATH="/root"
SERVER_PATH="/root/only-chat-be"
PM2_APP_NAME="only-chat-be"
APP_NAME=only-chat-be

echo "==== Building application locally ===="
npm run build

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
DIST_DIR="$(realpath "$SCRIPT_DIR/../dist")"
ZIP_FILE="$(dirname "$DIST_DIR")/dist.zip"

PACKAGE_FILE="$(realpath "$SCRIPT_DIR/../package.json")"
ENV_FILE="$(realpath "$SCRIPT_DIR/../.env.production")"
cp $PACKAGE_FILE $DIST_DIR
cp $ENV_FILE $DIST_DIR

# Change into the dist directory, zip its contents only
cd "$DIST_DIR"
zip -r "$ZIP_FILE" .

# Upload the zip file to the server
scp "$ZIP_FILE" "$SERVER:$ROOT_PATH"

echo "🖥️ Running PM2 on server..."
ssh -t $SERVER << EOF
  echo "Stopping application..."
  pm2 stop "$APP_NAME" || true
  
  echo "Extracting new files..."
  unzip -o '$ROOT_PATH/dist.zip' -d $SERVER_PATH
  cd $SERVER_PATH
  mv .env.production .env
  
  echo "Installing dependencies..."
  yarn install
  
  echo "Starting application..."
  pm2 start main.js --name "$APP_NAME" || pm2 restart "$APP_NAME" --update-env
  pm2 save
  
  echo "Clearing Nginx cache (if exists)..."
  if [ -d "/var/cache/nginx" ]; then
    rm -rf /var/cache/nginx/* || true
    systemctl restart nginx || true
  fi
  
  echo "Deployment completed successfully!"
EOF

rm "$ZIP_FILE"

echo "==== Done ===="
