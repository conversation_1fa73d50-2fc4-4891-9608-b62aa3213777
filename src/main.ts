import { NestFactory } from "@nestjs/core";
import { AppModule } from "./app.module";
import { ConfigService } from "@nestjs/config";
import { DocumentBuilder, SwaggerModule } from "@nestjs/swagger";
import { ValidationPipe } from "@nestjs/common";
import { RedisIoAdapter } from "./adapters/redis-io.adapter";
import { MicroserviceOptions, Transport } from "@nestjs/microservices";
import { NestExpressApplication } from "@nestjs/platform-express";
import { join } from "path";
import * as cookieParser from "cookie-parser";
import { Redis } from "ioredis";
import { REDIS_PUB, REDIS_SUB } from "./services/redis/redis.constants";

async function bootstrap() {
  // Create the main HTTP application
  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const configService = app.get(ConfigService);

  // Configure cookie parser
  app.use(cookieParser());

  // Configure static file serving for docs
  app.useStaticAssets(join(__dirname, "..", "docs"), { prefix: "/docs" });

  // Configure Redis adapter for WebSockets
  const pubClient = app.get<Redis>(REDIS_PUB);
  const subClient = app.get<Redis>(REDIS_SUB);
  const redisIoAdapter = new RedisIoAdapter(app, pubClient, subClient);
  redisIoAdapter.connectToRedis();
  app.useWebSocketAdapter(redisIoAdapter);

  // Apply global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: false,
      transform: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    })
  );

  // Configure CORS
  app.enableCors({
    origin: configService.get("CORS_ORIGIN") ?? "*",
    credentials: true,
  });

  // Configure API prefix
  app.setGlobalPrefix("api");

  // Configure Swagger
  const config = new DocumentBuilder()
    .setTitle("Only Chat API")
    .setDescription(
      "The Only Chat API documentation for REST endpoints. " +
        "For WebSocket/real-time chat API documentation, please refer to "
    )
    .setVersion("1.0")
    .addBearerAuth()
    .addTag(
      "Languages",
      'The API supports multiple languages. Use the "lang" query parameter or "Accept-Language" header to specify your preferred language.'
    )
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup("api/docs", app, document, {
    swaggerOptions: {
      defaultModelsExpandDepth: -1, // Hide schemas section
      tagsSorter: "alpha",
      operationsSorter: "alpha",
    },
  });

  // Optional: Connect to message broker for microservices
  const useKafka = configService.get<string>("USE_KAFKA") === "true";
  if (useKafka) {
    app.connectMicroservice<MicroserviceOptions>({
      transport: Transport.KAFKA,
      options: {
        client: {
          brokers: [
            configService.get<string>("KAFKA_BROKER") || "localhost:9092",
          ],
        },
        consumer: {
          groupId: "chat-consumer",
        },
      },
    });
    await app.startAllMicroservices();
    console.log("Kafka microservice is listening");
  }

  // Start the application
  const port = configService.get<number>("PORT") ?? 3000;
  await app.listen(port);
  console.log(`Application is running on: ${await app.getUrl()}`);
}
bootstrap().catch(console.error);
