import { <PERSON><PERSON><PERSON>, MiddlewareConsumer, NestModule } from "@nestjs/common";
import { APP_FILTER, APP_PIPE, APP_INTERCEPTOR } from "@nestjs/core";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { ConfigModule } from "./config";
import { DatabaseModule } from "./database";
import { UsersModule } from "./modules/users";
import { AuthModule } from "./modules/auth/auth.module";
import { WorkspaceModule } from "./modules/workspace/workspace.module";
import { ChatModule } from "./modules/chat/chat.module";
import { I18nConfigModule } from "./i18n/i18n.module";
import { I18nExceptionFilter } from "./common/filters/i18n-exception.filter";
import { I18nValidationPipe } from "./common/validators/i18n-validation.pipe";
import { MailModule } from "./mail";
import { GraphQLAppModule } from "./modules/graphql/graphql.module";
import { PluginsModule } from "./modules/plugins/plugins.module";
import { FileUploadModule } from "./modules/file-upload/file-upload.module";
import { ContactModule } from "./modules/contact";
import { ShortcutModule } from "./modules/shortcut";
import { HelpdeskModule } from "./plugins/helpdesk/helpdesk.module";
import { RedisModule } from "./services/redis/redis.module";
import { LoggingModule } from "./common/logging/logging.module";
import { LoggingInterceptor } from "./common/interceptors/logging.interceptor";
import { GraphQLLoggingInterceptor } from "./common/interceptors/graphql-logging.interceptor";
import { RequestLoggingMiddleware } from "./common/middleware/request-logging.middleware";
import { LoggingController } from "./common/controllers/logging.controller";
import { CommonModule } from "./common/common.module";

@Module({
  imports: [
    ConfigModule,
    DatabaseModule,
    RedisModule,
    LoggingModule,
    UsersModule,
    AuthModule,
    WorkspaceModule,
    ChatModule,
    I18nConfigModule,
    MailModule,
    GraphQLAppModule,
    PluginsModule,
    FileUploadModule,
    ContactModule,
    ShortcutModule,
    HelpdeskModule,
    CommonModule,
  ],
  controllers: [AppController, LoggingController],
  providers: [
    AppService,
    {
      provide: APP_FILTER,
      useClass: I18nExceptionFilter,
    },
    {
      provide: APP_PIPE,
      useClass: I18nValidationPipe,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: GraphQLLoggingInterceptor,
    },
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply request logging middleware to all routes
    consumer.apply(RequestLoggingMiddleware).forRoutes("*");
  }
}
