# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

interface Node {
  id: ID!
}

type User implements Node {
  id: ID!
  firstName: String
  lastName: String
  email: String!
  phone: String
  avatar: String
  role: String!
  status: String!
  isOnline: Boolean!
  lastActivityAt: DateTime
  createdAt: DateTime
  updatedAt: DateTime
  rawId: String!
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

type PageInfo {
  hasNextPage: Boolean!
  hasPreviousPage: Boolean!
  startCursor: String
  endCursor: String
}

type Workspace implements Node {
  id: ID!
  name: String!
  websiteUrl: String!
  websiteID: String!
  logo: String
  contactEmail: String
  contactPhone: String
  owner: User
  status: WorkspaceStatus
  role: WorkspaceMemberRole
  metadata: JSON
  rawId: String!
}

"""Status of a workspace"""
enum WorkspaceStatus {
  DRAFT
  PENDING
  INSTALLED
  CLOSED
}

"""Type of member"""
enum WorkspaceMemberRole {
  ADMIN
  DEVELOPER
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON

type WorkspaceTypeEdge {
  cursor: String!
  node: Workspace!
}

type WorkspaceMember implements Node {
  id: ID!
  user: User
  workspace: Workspace
  role: WorkspaceMemberRole
  status: WorkspaceMemberStatus
  createdAt: DateTime
  updatedAt: DateTime
  rawId: String!
}

"""Status of a workspace member"""
enum WorkspaceMemberStatus {
  PENDING
  APPROVED
  REJECTED
}

type WorkspaceMemberTypeEdge {
  cursor: String!
  node: WorkspaceMember!
}

type Message implements Node {
  id: ID!
  conversationId: ID!
  content: String!
  sender: MessageSender!
  user: User
  replyTo: Message
  type: MessageTypeEnum
  status: MessageStatus!
  metadata: JSON
  createdAt: DateTime!
  updatedAt: DateTime!
  rawId: String!
}

"""Type of message sender"""
enum MessageSender {
  GUEST
  AGENT
  SYSTEM
}

"""Type of message content"""
enum MessageTypeEnum {
  TEXT
  FILE
  IMAGE
  SYSTEM
  NOTE
  RESOLVED
}

"""Status of a message"""
enum MessageStatus {
  SENT
  DELIVERED
  READ
  FAILED
}

type MessageTypeEdge {
  cursor: String!
  node: Message!
}

type MessageConnection {
  edges: [MessageTypeEdge!]!
  pageInfo: PageInfo!
}

type ContactContext {
  ip: String
  countryCode: String
  countryName: String
  city: String
  region: String
  latitude: Float
  longitude: Float
  timezone: String
  browser: String
  os: String
  language: String
}

type CompanyInfo {
  company: String
  jobTitle: String
  jobRole: String
  website: String
  city: String
  country: String
  employees: Int
}

type Contact implements Node {
  id: ID!
  guestId: String
  avatar: String
  name: String!
  email: String
  phoneNumber: String
  address: String
  website: String
  gender: ContactGender
  notification: Boolean!
  companyInfo: CompanyInfo
  segments: [String!]!
  metadata: JSON!
  context: ContactContext
  notes: String
  isOnline: Boolean!
  lastActivityAt: DateTime
  lastConversations: [Conversation!]
  createdAt: DateTime
  updatedAt: DateTime
  rawId: String!
}

"""The gender of the contact"""
enum ContactGender {
  MALE
  FEMALE
}

type ContactPaginationEdge {
  cursor: String!
  node: Contact!
}

type ContactPagination {
  edges: [ContactPaginationEdge!]!
  pageInfo: PageInfo!
  totalCount: Int!
}

type ContactTypeEdge {
  cursor: String!
  node: Contact!
}

type ContactConnection {
  edges: [ContactTypeEdge!]!
  pageInfo: PageInfo!
}

type Conversation implements Node {
  id: ID!
  contact: Contact
  assignedTo: User
  participants: [User!]
  resolved: Boolean
  segments: [String!]
  subject: String
  metadata: JSON
  createdAt: DateTime
  updatedAt: DateTime
  lastActivityAt: DateTime
  closedAt: DateTime
  unreadCount: Int
  latestMessage: Message
  replyTo: Message
  rawId: String!
}

type ConversationTypeEdge {
  cursor: String!
  node: Conversation!
}

type ConversationConnection {
  edges: [ConversationTypeEdge!]!
  pageInfo: PageInfo!
}

type HelpDeskCategory implements Node {
  id: ID!
  name: String!
  image: String
  slug: String!
  desc: String
  workspace: Workspace
  createdAt: DateTime
  updatedAt: DateTime
}

type HelpDeskCategoryPaginationEdge {
  cursor: String!
  node: HelpDeskCategory!
}

type HelpDeskCategorySection implements Node {
  id: ID!
  name: String!
  articleCount: Int!
  createdAt: DateTime
  updatedAt: DateTime
}

type HelpDeskCategorySectionPaginationEdge {
  cursor: String!
  node: HelpDeskCategorySection!
}

type HelpDeskArticle implements Node {
  id: ID!
  workspace: Workspace
  title: String!
  content: String!
  slug: String!
  tags: [String!]!
  status: String!
  viewCount: Int!
  createdAt: DateTime
  updatedAt: DateTime
}

type HelpDeskArticlePaginationEdge {
  cursor: String!
  node: HelpDeskArticle!
}

type Query {
  me: User
  workspaces: [Workspace!]!
  operators: [WorkspaceMember!]!
  conversations(assignedToMe: Boolean, first: Float, after: String, last: Float, before: String): ConversationConnection!
  messages(conversationId: ID!, first: Float, after: String, last: Float, before: String): MessageConnection!
  contacts(args: PaginationArgs!, keyword: String): ContactPagination!
  liveContacts(first: Float, after: String, last: Float, before: String): ContactConnection!
  node(id: ID!): Node
}

input PaginationArgs {
  first: Int = 10
  offset: Int = 0
  last: Int
}