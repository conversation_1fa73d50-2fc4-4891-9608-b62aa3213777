import { Injectable, Logger } from "@nestjs/common";
import { RedisClientService } from "./redis-client.service";
import { AuthenticatedSocket } from "src/modules/chat/gateways/handlers/connection.handler";

@Injectable()
export class SocketRedisService {
  constructor(private readonly redisClientService: RedisClientService) {}

  async saveSocketId(client: AuthenticatedSocket) {
    const userId = client.user?.id ?? client.contact?.id;
    if (!userId) {
      return;
    }

    const key = `socket:${userId}:sockets`;
    await this.redisClientService.sadd(key, client.id);
  }

  getSocketIds(userId: string) {
    return this.redisClientService.smembers(`socket:${userId}:sockets`);
  }

  async removeSocketId(client: AuthenticatedSocket) {
    const userId = client.user?.id ?? client.contact?.id;
    if (!userId) {
      return;
    }

    const key = `socket:${userId}:sockets`;
    return this.redisClientService.srem(key, client.id);
  }
}
