import { Injectable } from "@nestjs/common";
import { RedisClientService } from "./redis-client.service";

@Injectable()
export class TypingRedisService {
  constructor(private readonly redisClientService: RedisClientService) {}

  async canEmitTypingEvent(
    userId: string,
    conversationId: string,
    cooldownMs = 60000
  ): Promise<boolean> {
    const key = `typing:${conversationId}:${userId}`;
    const lastTypingTime = await this.redisClientService.get(key);
    const now = Date.now();

    if (!lastTypingTime || now - parseInt(lastTypingTime) >= cooldownMs) {
      await this.redisClientService.set(key, now.toString()); // Expire after 1 minute
      return true;
    }

    return false;
  }
}
