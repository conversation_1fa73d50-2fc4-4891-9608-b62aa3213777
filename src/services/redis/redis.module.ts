import { Module, Global } from "@nestjs/common";
import { ConfigModule, ConfigService } from "@nestjs/config";
import Redis from "ioredis";
import { REDIS_CLIENT, REDIS_PUB, REDIS_SUB } from "./redis.constants";
import { TypingRedisService } from "./typing.redis.service";
import { RedisClientService } from "./redis-client.service";
import { UserStatusRedisService } from "./user-status.redis.service";
import { SocketRedisService } from "./socket.redis.service";
import { GoogleUserRedisService } from "./google-user.redis.service";

@Global() // 👈 để dùng ở mọi module không cần import lại
@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: REDIS_CLIENT,
      useFactory: (configService: ConfigService) => {
        const redisUrl = configService.get<string>("REDIS_URL");
        return redisUrl
          ? new Redis(redisUrl)
          : new Redis({ host: "localhost", port: 6379 });
      },
      inject: [ConfigService],
    },
    {
      provide: REDIS_PUB,
      useFactory: (configService: ConfigService) => {
        const redisUrl = configService.get<string>("REDIS_URL");
        return redisUrl
          ? new Redis(redisUrl)
          : new Redis({ host: "localhost", port: 6379 });
      },
      inject: [ConfigService],
    },
    {
      provide: REDIS_SUB,
      useFactory: (configService: ConfigService) => {
        const redisUrl = configService.get<string>("REDIS_URL");
        return redisUrl
          ? new Redis(redisUrl)
          : new Redis({ host: "localhost", port: 6379 });
      },
      inject: [ConfigService],
    },
    RedisClientService,
    UserStatusRedisService,
    SocketRedisService,
    TypingRedisService,
    GoogleUserRedisService,
  ],
  exports: [
    REDIS_CLIENT,
    REDIS_PUB,
    REDIS_SUB,
    UserStatusRedisService,
    SocketRedisService,
    TypingRedisService,
    GoogleUserRedisService,
  ],
})
export class RedisModule {}
