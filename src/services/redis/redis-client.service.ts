import { Inject, Injectable, OnModuleDestroy } from "@nestjs/common";
import { REDIS_CLIENT } from "./redis.constants";
import Redis from "ioredis";

@Injectable()
export class RedisClientService implements OnModuleDestroy {
  constructor(@Inject(REDIS_CLIENT) private readonly redis: Redis) {}

  set(key: string, value: string) {
    return this.redis.set(key, value);
  }

  get(key: string) {
    return this.redis.get(key);
  }

  mget(keys: string[]) {
    return this.redis.mget(keys);
  }

  sadd(key: string, value: string) {
    return this.redis.sadd(key, value);
  }

  smembers(key: string) {
    return this.redis.smembers(key);
  }

  srem(key: string, value: string) {
    return this.redis.srem(key, value);
  }

  expire(key: string, seconds: number) {
    return this.redis.expire(key, seconds);
  }

  del(key: string) {
    return this.redis.del(key);
  }

  keys(pattern: string) {
    return this.redis.keys(pattern);
  }

  flushAll() {
    return this.redis.flushall();
  }

  async onModuleDestroy() {
    await this.redis.quit();
  }
}
