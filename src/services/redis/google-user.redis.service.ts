import { Injectable } from "@nestjs/common";
import { RedisClientService } from "./redis-client.service";
import { GoogleUser } from "src/modules/auth/interfaces/google-payload.interface";

const expiration = 60; // 1 minute
const GOOGLE_USER_SESSION_PREFIX = "google-user-session";

@Injectable()
export class GoogleUserRedisService {
  constructor(private readonly redisClientService: RedisClientService) {}

  async storeGoogleUser(googleUser: GoogleUser): Promise<string> {
    const token =
      Math.random().toString(36).substring(2, 15) +
      Math.random().toString(36).substring(2, 15);
    const tokenKey = `${GOOGLE_USER_SESSION_PREFIX}:${token}`;
    await this.redisClientService.set(tokenKey, JSON.stringify(googleUser));
    await this.redisClientService.expire(tokenKey, expiration);

    return token;
  }

  async getGoogleUser(token: string): Promise<GoogleUser | null> {
    const key = `${GOOGLE_USER_SESSION_PREFIX}:${token}`;
    const value = await this.redisClientService.get(key);
    if (!value) {
      return null;
    }
    return JSON.parse(value) as GoogleUser;
  }
}
