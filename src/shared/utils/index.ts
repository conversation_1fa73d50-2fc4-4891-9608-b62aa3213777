/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import * as randomstring from "randomstring";
export const generateFileName = () => {
  const fileName = randomstring.generate({
    length: 12,
    charset: "alphabetic",
  });
  return fileName;
};
