import { SendMessageDto } from "src/modules/chat/dto/send-message.dto";

interface ResolvedConversationEvent {
  conversationId: string;
  message: SendMessageDto;
  resolvedBy: string;
  resolved: boolean;
}

export class ResolvedConversationEventDto implements ResolvedConversationEvent {
  conversationId: string;
  message: SendMessageDto;
  resolvedBy: string;
  resolved: boolean;

  constructor({
    conversationId,
    message,
    resolvedBy,
    resolved,
  }: ResolvedConversationEvent) {
    this.conversationId = conversationId;
    this.message = message;
    this.resolvedBy = resolvedBy;
    this.resolved = resolved;
  }
}
