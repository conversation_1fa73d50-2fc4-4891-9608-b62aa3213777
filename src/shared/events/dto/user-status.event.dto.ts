interface UserStatusEvent {
  userId?: string;
  contactId?: string;
  workspaceId: string;
  isOnline: boolean;
  lastActivityAt?: Date;
}

export class UserStatusEventDto implements UserStatusEvent {
  userId?: string;
  contactId?: string;
  workspaceId: string;
  isOnline: boolean;
  lastActivityAt?: Date;

  constructor({
    userId,
    contactId,
    workspaceId,
    isOnline,
    lastActivityAt,
  }: UserStatusEvent) {
    this.userId = userId;
    this.contactId = contactId;
    this.workspaceId = workspaceId;
    this.isOnline = isOnline;
    this.lastActivityAt = lastActivityAt;
  }
}
