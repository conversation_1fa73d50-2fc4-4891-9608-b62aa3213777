/* eslint-disable @typescript-eslint/prefer-promise-reject-errors */
/* eslint-disable @typescript-eslint/no-unsafe-call */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import { Injectable } from "@nestjs/common";
import * as fs from "fs";

import * as AWS from "aws-sdk";
import * as S3 from "aws-sdk/clients/s3";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class FileUploadService {
  s3: S3;
  bucket: string;

  constructor(configService: ConfigService) {
    const spaceConfig = configService.get("space");

    this.bucket = spaceConfig["bucket"];
    this.s3 = new S3({
      endpoint: new AWS.Endpoint(spaceConfig["endpoint"]),
      accessKeyId: spaceConfig["accessKeyId"],
      secretAccessKey: spaceConfig["secretAccessKey"],
      signatureVersion: "v4",
    });
  }

  async uploadFile(
    file: Express.Multer.File,
    folder: string,
    fileName?: string
  ) {
    return new Promise((resolve, reject) => {
      const name = fileName != null ? fileName : String(file.originalname);
      this.s3.putObject(
        {
          Body: file.buffer,
          Bucket: this.bucket,
          Key: `${folder}/${name}`,
          ACL: "public-read",
          ContentType: "image/jpeg",
        },
        (err, data) => {
          console.log({ err, data });

          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        }
      );
    });
  }

  async uploadLocalFile(filePath: string, folder: string, fileName: string) {
    const buffer = fs.readFileSync(filePath);
    return new Promise((resolve, reject) => {
      this.s3.putObject(
        {
          Body: buffer,
          Bucket: this.bucket,
          Key: `${folder}/${fileName}`,
          ACL: "public-read",
          ContentType: "image/jpeg",
        },
        (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        }
      );
    });
  }

  async deleteFile(folder: string, fileName: string) {
    return new Promise((resolve, reject) => {
      this.s3.deleteObject(
        {
          Bucket: this.bucket,
          Key: `${folder}/${fileName}`,
        },
        (err, data) => {
          if (err) {
            reject(err);
          } else {
            resolve(data);
          }
        }
      );
    });
  }
}
