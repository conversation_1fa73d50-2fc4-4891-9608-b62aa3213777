/* eslint-disable @typescript-eslint/only-throw-error */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */
import {
  Controller,
  Post,
  Request,
  UploadedFile,
  UseGuards,
  UseInterceptors,
  HttpStatus,
  HttpException,
} from "@nestjs/common";
import { FileInterceptor } from "@nestjs/platform-express";
import { ApiBearerAuth, ApiBody, ApiConsumes, ApiTags } from "@nestjs/swagger";
import { FileUploadService } from "./file-upload.service";
import * as Constants from "./constants";
import * as path from "path";
import { getFileUploadUrl } from "./file-upload-utils";
import { JwtAuthGuard } from "src/modules/auth/guards/jwt-auth.guard";
import { generateFileName } from "@app/shared";

export const imageFileFilter = (
  req: any,
  file: { originalname: string },
  callback: (error: Error | null, acceptFile: boolean) => void
) => {
  if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
    return callback(new Error("Only image files are allowed!"), false);
  }
  callback(null, true);
};

export const fileFilter = (
  req: any,
  file: { originalname: string },
  callback: (error: Error | null, acceptFile: boolean) => void
) => {
  if (!file.originalname.match(/\.(jpg|jpeg|png|gif|pdf|doc|docx)$/)) {
    return callback(new Error("File is not allowed!"), false);
  }
  callback(null, true);
};

@ApiTags("FileUpload")
@Controller("file-upload")
export class FileUploadController {
  constructor(private readonly service: FileUploadService) {}

  @Post("/image")
  @ApiBearerAuth()
  @UseGuards(JwtAuthGuard)
  @UseInterceptors(
    FileInterceptor("image", {
      fileFilter: imageFileFilter,
    })
  )
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        image: {
          type: "string",
          format: "binary",
        },
      },
    },
  })
  async uploadImage(@Request() req, @UploadedFile() file: Express.Multer.File) {
    try {
      const imgPath = Constants.imagesFolder;
      const fileName = generateFileName() + path.extname(file.originalname);
      const success = await this.service.uploadFile(file, imgPath, fileName);
      if (success) {
        const filePath = imgPath + "/" + fileName;
        return { fileUrl: getFileUploadUrl(filePath) };
      } else {
        throw "Upload Failed";
      }
    } catch (error) {
      throw new HttpException(error, HttpStatus.BAD_REQUEST);
    }
  }

  @Post("/file")
  @UseInterceptors(
    FileInterceptor("file", {
      fileFilter: imageFileFilter,
      limits: { fileSize: 10 * 1024 * 1024 }, //10MB
    })
  )
  @ApiConsumes("multipart/form-data")
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        file: {
          type: "string",
          format: "binary",
        },
      },
    },
  })
  async uploadFile(@Request() req, @UploadedFile() file: Express.Multer.File) {
    try {
      const imgPath = Constants.imagesFolder;
      const fileName = generateFileName() + path.extname(file.originalname);
      const success = await this.service.uploadFile(file, imgPath, fileName);
      if (success) {
        const filePath = imgPath + "/" + fileName;
        return { fileUrl: getFileUploadUrl(filePath) };
      } else {
        throw "Upload Failed";
      }
    } catch (error) {
      throw new HttpException(error, HttpStatus.BAD_REQUEST);
    }
  }
}
