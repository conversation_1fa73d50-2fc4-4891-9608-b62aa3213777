import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsInt, IsN<PERSON>ber, IsOptional, Min } from "class-validator";

export class PaginationResponseDto<T> {
  data: Array<T>;

  @IsInt()
  total: number;

  @IsInt()
  page: number;

  @IsBoolean()
  hasNextPage: boolean;
}

export class PaginationInputDto {
  @ApiProperty({
    description: "Page number (default: 1)",
    required: false,
    example: 1,
  })
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @IsInt()
  @Min(1)
  page: number = 1;

  @ApiProperty({
    description: "Items per page (default: 10)",
    required: false,
    example: 10,
  })
  @Type(() => Number)
  @IsNumber()
  @IsOptional()
  @IsInt()
  limit: number = 10;
}
