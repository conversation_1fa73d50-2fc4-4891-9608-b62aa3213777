import { Types } from "mongoose";

export abstract class BaseEntity {
  id: string;
  createdAt?: Date;
  updatedAt?: Date;

  protected assignFields(doc: BaseDocument<BaseEntity>) {
    this.id = doc._id.toString();
    this.createdAt = doc.createdAt;
    this.updatedAt = doc.updatedAt;
  }

  toGraphQL(): any {}
}

export type BaseDocument<B extends { id: string }> = Omit<B, "id"> & {
  _id: Types.ObjectId;
};
