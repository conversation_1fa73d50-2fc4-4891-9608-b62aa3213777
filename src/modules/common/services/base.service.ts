/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Model, Types } from "mongoose";
import { fromGlobalId, toGlobalId } from "graphql-relay";
import { ConnectionArgs } from "src/modules/graphql/types/connection.args";
import { NotFoundException } from "@nestjs/common";
import { BaseEntity } from "../entities/base.entity";
import { PaginationArgs } from "src/modules/graphql/types/pagination.args";

type EntityConstructor<E> = new (...args: any[]) => E;

export abstract class BaseService<E extends BaseEntity> {
  constructor(
    protected readonly model: Model<E>,
    protected entityClass: EntityConstructor<E>,
    protected readonly entityName: string //this value is match with ObjectType of GraphQL, use to check in NodeResolver
  ) {}

  protected async findWithConnection(
    query: any,
    args: ConnectionArgs
  ): Promise<any> {
    const { first, after, last, before } = args;

    const limit = first ?? last ?? 10;
    const isBackward = !!last;

    const updatedQuery = { ...query };
    if (after) {
      updatedQuery._id = { $gt: new Types.ObjectId(fromGlobalId(after).id) };
    } else if (before) {
      updatedQuery._id = { $lt: new Types.ObjectId(fromGlobalId(before).id) };
    }

    const sort: any = { _id: isBackward ? -1 : 1 };

    let items = await this.model
      .find(updatedQuery)
      .sort(sort)
      .limit(limit + 1); // +1 to check hasNext/hasPrevious

    const hasExtraItem = items.length > limit;
    if (hasExtraItem) items = items.slice(0, limit);

    if (isBackward) items = items.reverse();

    const edges = items.map((item) => ({
      node: new this.entityClass(item).toGraphQL(),
      cursor: toGlobalId(this.entityName, item._id.toString()),
    }));

    const startCursor = edges[0]?.cursor;
    const endCursor = edges[edges.length - 1]?.cursor;

    return {
      edges,
      pageInfo: {
        hasNextPage: !isBackward && hasExtraItem,
        hasPreviousPage: isBackward && hasExtraItem,
        startCursor,
        endCursor,
      },
    };
  }

  protected async findWithPagination(
    query: any,
    args: PaginationArgs
  ): Promise<any> {
    const { first, offset, last } = args;

    const limit = first ?? last ?? 10;

    let updatedQuery = this.model
      .find(query)
      .skip(offset ?? 0)
      .limit(limit);

    if (last) {
      updatedQuery = updatedQuery.sort({ _id: -1 });
    }

    const items = await updatedQuery.exec();

    const edges = items.map((item) => ({
      node: new this.entityClass(item).toGraphQL(),
      cursor: toGlobalId(this.entityName, item._id.toString()),
    }));

    const startCursor = edges[0]?.cursor;
    const endCursor = edges[edges.length - 1]?.cursor;

    const totalCount = await this.model.countDocuments(query);

    return {
      edges,
      pageInfo: {
        hasNextPage: (offset ?? 0) + limit < totalCount,
        hasPreviousPage: (offset ?? 0) > 0,
        startCursor,
        endCursor,
      },
      totalCount,
    };
  }

  protected async findOne(id: string): Promise<E> {
    const doc = await this.model.findById(id).exec();
    if (!doc) {
      throw new NotFoundException(` ID ${id} not found`);
    }
    return new this.entityClass(doc);
  }

  protected async create(data: any): Promise<E> {
    const item = new this.model(data);
    const doc = await item.save();
    return new this.entityClass(doc);
  }

  protected async update(id: string, data: any): Promise<E> {
    const item = await this.model.findById(id).exec();
    if (!item) {
      throw new NotFoundException(` ID ${id} not found`);
    }
    const doc = await this.model
      .findByIdAndUpdate(id, data, { new: true })
      .exec();
    return new this.entityClass(doc);
  }

  protected async remove(id: string): Promise<boolean> {
    const result = await this.model.findByIdAndDelete(id).exec();
    return !!result;
  }

  protected async findManyByIds(ids: string[]): Promise<E[]> {
    const items = await this.model.find({ _id: { $in: ids } }).exec();
    return items.map((item) => new this.entityClass(item));
  }
}
