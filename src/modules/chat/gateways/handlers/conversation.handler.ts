import { Logger } from "@nestjs/common";
import { WsException } from "@nestjs/websockets";
import { Server } from "socket.io";
import { ChatServiceInterface } from "../../interfaces/chat-service.interface";
import { AuthenticatedSocket } from "./connection.handler";
import {
  CONVERSATION_ROOM_PREFIX,
  SERVER_UPDATED_CONVERSATION_EVENT,
} from "../gateway.constants";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import { UPDATED_CONVERSATION_EVENT } from "@app/shared/events/events.constants";
import { toGlobalId } from "graphql-relay";

export class ConversationHandler {
  private readonly logger = new Logger(ConversationHandler.name);

  constructor(
    private readonly chatService: ChatServiceInterface,
    private eventEmitter: EventEmitter2,
    private readonly server: Server
  ) {}

  async handleOpenConversation(
    client: AuthenticatedSocket,
    payload: { conversationId: string }
  ) {
    try {
      const userId = client.user?.id;
      if (!userId) {
        throw new WsException("Unauthorized");
      }

      this.logger.log(
        `${client.user?.email} open conversation: ${payload.conversationId}`
      );
      await this.chatService.markMessagesAsRead(payload.conversationId, userId);
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error handling open conversation: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  async handleCloseConversation(
    client: AuthenticatedSocket,
    payload: { conversationId: string }
  ) {
    try {
      const userId = client.user?.id;
      if (!userId) {
        throw new WsException("Unauthorized");
      }

      this.logger.log(
        `${client.user?.email} close conversation: ${payload.conversationId}`
      );
      await this.chatService.markMessagesAsRead(payload.conversationId, userId);
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error handling open conversation: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  @OnEvent(UPDATED_CONVERSATION_EVENT)
  handleUpdatedConversation(data: { conversationId: string }) {
    const resData = {
      conversationId: data.conversationId,
      conversationRawId: toGlobalId("Conversation", data.conversationId),
    };

    this.server
      .to(`${CONVERSATION_ROOM_PREFIX}${data.conversationId}`)
      .emit(SERVER_UPDATED_CONVERSATION_EVENT, resData);

    this.logger.log(
      `Emit updated conversation event to ${`${CONVERSATION_ROOM_PREFIX}${data.conversationId}`} with data ${JSON.stringify(
        resData
      )}`
    );
  }
}
