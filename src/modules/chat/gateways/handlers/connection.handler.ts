import { Logger } from "@nestjs/common";
import { Socket } from "socket.io";
import { JwtService } from "@nestjs/jwt";
import { ChatServiceInterface } from "../../interfaces/chat-service.interface";
import { JwtPayload } from "src/modules/auth/interfaces/jwt-payload.interface";
import {
  CONVERSATION_ROOM_PREFIX,
  WORKSPACE_ROOM_PREFIX,
} from "../gateway.constants";
import { SocketRedisService } from "src/services/redis/socket.redis.service";
import { ContactServiceInterface } from "src/modules/contact";
import { AnalyticsService } from "src/common/services/analytics.service";

export interface AuthenticatedSocket extends Socket {
  user?: {
    id: string;
    email: string;
  };
  contact?: {
    id: string;
    guestId?: string;
  };
  workspaceId?: string;
}

export class ConnectionHandler {
  private readonly logger = new Logger(ConnectionHandler.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly chatService: ChatServiceInterface,
    private readonly socketRedisService: SocketRedisService,
    private readonly contactService: ContactServiceInterface,
    private readonly analyticsService: AnalyticsService
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = client.handshake.auth.token as string;
      const guestId = client.handshake.auth.guestIdentifier as string;
      const workspaceId = client.handshake.auth.workspaceId as string;

      if (token) {
        await this.handleUserConnection(client, token);
      } else if (guestId) {
        await this.handleGuestConnection(client, guestId, workspaceId);
      } else {
        this.logger.error("No authentication provided");
        client.disconnect();
        return;
      }

      await this.joinConversationRooms(client);
      await this.joinWorkspace(client);
    } catch (error) {
      this.logger.error(
        `Connection error: ${error instanceof Error ? error.message : String(error)}`
      );
      client.disconnect();
    }
  }

  private async handleUserConnection(
    client: AuthenticatedSocket,
    token: string
  ) {
    const payload = this.jwtService.verify<JwtPayload>(token);
    if (payload) {
      if (!payload.workspaceId) {
        throw new Error("No workspace provided");
      }
      client.user = {
        id: payload.sub,
        email: payload.email,
      };
      client.workspaceId = payload.workspaceId;

      await this.chatService.updateUserOnlineStatus(
        client.user.id,
        client.workspaceId,
        true
      );
      await this.socketRedisService.saveSocketId(client);
      this.logger.log(
        `User connected: ${client.user.id} ${client.user.email} to workspace ${client.workspaceId} with socket id: ${client.id}`
      );
    } else {
      throw new Error("Invalid token payload");
    }
  }

  private async handleGuestConnection(
    client: AuthenticatedSocket,
    guestId: string,
    workspaceId: string
  ) {
    client.workspaceId = workspaceId;
    const contactDoc = await this.contactService.createTempContact(
      workspaceId,
      guestId
    );
    if (contactDoc) {
      client.contact = {
        id: contactDoc._id.toString(),
        guestId: contactDoc.guestId,
      };
      this.logger.log(`Guest ${guestId} connected to workspace ${workspaceId}`);
      await this.chatService.updateContactOnlineStatus(
        contactDoc._id.toString(),
        workspaceId,
        true
      );
      await this.socketRedisService.saveSocketId(client);

      const analyticsData = this.analyticsService.extractAnalyticsData(
        client.request
      );
      await this.contactService.updateContactContext(
        contactDoc._id.toString(),
        analyticsData
      );
    } else {
      throw new Error("Contact not found");
    }
  }

  private async joinConversationRooms(client: AuthenticatedSocket) {
    if (!client.workspaceId) {
      return;
    }
    if (client.user) {
      const userConversations = await this.chatService.getConversationsByUser(
        client.user.id,
        client.workspaceId
      );
      for (const conv of userConversations) {
        this.logger.log(`${client.user?.email} joined ${conv._id.toString()}`);
        await client.join(`${CONVERSATION_ROOM_PREFIX}${conv._id.toString()}`);
      }
    } else if (client.contact) {
      const conversation = await this.chatService.getConversationByContactId(
        client.contact?.id ?? "",
        client.workspaceId
      );
      if (conversation) {
        this.logger.log(
          `${client.contact?.guestId} joined ${conversation._id.toString()}`
        );
        await client.join(
          `${CONVERSATION_ROOM_PREFIX}${conversation._id.toString()}`
        );
      }
    }
  }

  private async joinWorkspace(client: AuthenticatedSocket) {
    if (!client.workspaceId) {
      return;
    }
    await client.join(`${WORKSPACE_ROOM_PREFIX}${client.workspaceId}`);
  }
}
