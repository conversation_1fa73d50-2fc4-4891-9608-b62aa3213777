import { Logger } from "@nestjs/common";
import { AuthenticatedSocket } from "./connection.handler";
import { ChatServiceInterface } from "../../interfaces/chat-service.interface";
import { SocketRedisService } from "src/services/redis/socket.redis.service";

export class DisconnectHandler {
  private readonly logger = new Logger(DisconnectHandler.name);

  constructor(
    private readonly chatService: ChatServiceInterface,
    private readonly socketRedisService: SocketRedisService
  ) {}

  async handleDisconnect(client: AuthenticatedSocket) {
    try {
      if (client.user != null && client.user?.id) {
        await this.handleUserDisconnect(client);
      } else if (client.contact != null && client.contact?.id) {
        await this.handleGuestDisconnect(client);
      }
      await this.socketRedisService.removeSocketId(client);
    } catch (error) {
      this.logger.error(
        `Error during disconnect: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  private async handleUserDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`User disconnected: ${client.user?.email}`);
    await this.chatService.updateUserOnlineStatus(
      client.user?.id ?? "",
      client.workspaceId ?? "",
      false
    );
  }

  private async handleGuestDisconnect(client: AuthenticatedSocket) {
    this.logger.log(`Guest disconnected: ${client.contact?.guestId}`);
    await this.chatService.updateContactOnlineStatus(
      client.contact?.id ?? "",
      client.workspaceId ?? "",
      false
    );
  }
}
