import { Logger } from "@nestjs/common";
import { WsException } from "@nestjs/websockets";
import { Server } from "socket.io";
import { ChatServiceInterface } from "../../interfaces/chat-service.interface";
import { SendMessageDto } from "../../dto/send-message.dto";
import { AuthenticatedSocket } from "./connection.handler";
import { TypingRedisService } from "../../../../services/redis/typing.redis.service";
import { MessageSender } from "../../schemas/message.schema";
import {
  CONVERSATION_ROOM_PREFIX,
  SERVER_MESSAGE_EVENT,
  SERVER_TYPING_EVENT,
} from "../gateway.constants";
import { EventEmitter2, OnEvent } from "@nestjs/event-emitter";
import {
  RESOLVED_CONVERSATION_EVENT,
  UPDATED_CONVERSATION_EVENT,
} from "@app/shared/events/events.constants";
import { MessageDocument } from "../../entities/message.entity";
import { ResolvedConversationEventDto } from "@app/shared/events/dto/resolved-conversation.event.dto";

export class MessageHandler {
  private readonly logger = new Logger(MessageHandler.name);

  constructor(
    private readonly chatService: ChatServiceInterface,
    private readonly typingRedisService: TypingRedisService,
    private eventEmitter: EventEmitter2,
    private readonly server: Server
  ) {}

  private async sendMessage(
    conversationId: string,
    message: SendMessageDto,
    userId?: string
  ): Promise<MessageDocument> {
    const savedMessage = await this.chatService.sendMessage({
      ...message,
      conversationId,
      userId: userId,
      sender: userId != null ? MessageSender.AGENT : MessageSender.GUEST,
    });

    if (userId != null) {
      this.logger.log(
        `${userId} sent: ${JSON.stringify(savedMessage)} to ${conversationId}`
      );
    } else {
      this.logger.log(
        `GUEST sent: ${JSON.stringify(savedMessage)} ${conversationId}`
      );
    }

    await this.chatService.updateConversation(conversationId, {
      latestMessageId: savedMessage._id.toString(),
    });
    this.logger.log(
      `update conversation ${conversationId} with latest message ${savedMessage._id.toString()}`
    );
    this.eventEmitter.emit(UPDATED_CONVERSATION_EVENT, {
      conversationId,
    });
    return savedMessage;
  }

  async handleSendMessage(
    client: AuthenticatedSocket,
    payload: { conversationId: string; message: SendMessageDto }
  ) {
    try {
      if (!client.user && !client.contact) {
        throw new WsException("Unauthorized");
      }

      const { conversationId, message } = payload;

      this.logger.log(
        `${client.user?.email ?? client.contact?.guestId} send message ${JSON.stringify(payload)}`
      );

      const savedMessage = await this.sendMessage(
        conversationId,
        message,
        client.user?.id
      );

      client
        .to(`${CONVERSATION_ROOM_PREFIX}${conversationId}`)
        .emit(SERVER_MESSAGE_EVENT, savedMessage);

      return { success: true, messageId: savedMessage._id.toString() };
    } catch (error) {
      this.logger.error(
        `Error sending message: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  async handleTypingStart(
    client: AuthenticatedSocket,
    payload: { conversationId: string }
  ) {
    try {
      const userId = client.user?.id || client.contact?.id;
      if (!userId) {
        throw new WsException("Unauthorized");
      }

      this.logger.log(
        `${client.user?.email ?? client.contact?.guestId} is typing`
      );
      // Check if we can emit the typing event (debouncing)
      const canEmit = await this.typingRedisService.canEmitTypingEvent(
        userId,
        payload.conversationId
      );

      if (canEmit) {
        this.logger.log(
          `emit user_typing (isTyping: true) to ${payload.conversationId}`
        );
        client
          .to(`${CONVERSATION_ROOM_PREFIX}${payload.conversationId}`)
          .emit(SERVER_TYPING_EVENT, {
            userId: client.user?.id,
            isGuest: client.contact?.id != null,
            conversationId: payload.conversationId,
            isTyping: true,
          });
      } else {
        this.logger.log(`we can't emit user_typing event because debouncing`);
      }
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error handling typing start: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  handleTypingStop(
    client: AuthenticatedSocket,
    payload: { conversationId: string }
  ) {
    try {
      const userId = client.user?.id || client.contact?.id;
      if (!userId) {
        throw new WsException("Unauthorized");
      }

      this.logger.log(
        `${client.user?.email ?? client.contact?.guestId} stop typing`
      );
      this.logger.log(
        `emit user_typing (isTyping: false) to ${payload.conversationId}`
      );

      client
        .to(`${CONVERSATION_ROOM_PREFIX}${payload.conversationId}`)
        .emit(SERVER_TYPING_EVENT, {
          userId: client.user?.id,
          isGuest: client.contact?.id != null,
          conversationId: payload.conversationId,
          isTyping: false,
        });
      return { success: true };
    } catch (error) {
      this.logger.error(
        `Error handling typing stop: ${error instanceof Error ? error.message : String(error)}`
      );
      throw new WsException(
        error instanceof Error ? error.message : String(error)
      );
    }
  }

  @OnEvent(RESOLVED_CONVERSATION_EVENT)
  async handleResolvedConversation(data: ResolvedConversationEventDto) {
    const savedMessage = await this.sendMessage(
      data.conversationId,
      data.message,
      data.resolvedBy
    );
    this.server
      .to(`${CONVERSATION_ROOM_PREFIX}${data.conversationId}`)
      .emit(SERVER_MESSAGE_EVENT, savedMessage);
  }
}
