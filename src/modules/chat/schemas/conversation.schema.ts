import { <PERSON><PERSON>, <PERSON>hem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Schema as MongooseSchema } from "mongoose";
import { User } from "../../users/schemas/user.schema";
import { Workspace } from "../../workspace/schemas/workspace.schema";
import { Message } from "./message.schema";
import { Contact } from "src/modules/contact/schemas/contact.schema";

@Schema({ timestamps: true })
export class Conversation {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: true,
  })
  workspace: Workspace;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "Contact" })
  contact: Contact;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User" })
  assignedTo: User;

  @Prop([{ type: MongooseSchema.Types.ObjectId, ref: "User" }])
  participants: User[];

  @Prop({ default: false })
  resolved: boolean;

  @Prop({ type: [String], default: [] })
  segments: string[];

  @Prop()
  subject: string;

  @Prop({ type: Object })
  metadata: Record<string, any>;

  @Prop({ default: Date.now })
  lastActivityAt: Date;

  @Prop()
  closedAt: Date;

  @Prop({
    type: Map,
    of: Date,
    default: {},
  })
  lastReadTimestamps: Map<string, Date>; // key: userId, value: last read timestamp

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "Message" })
  latestMessage: Message;

  @Prop({ default: false })
  deleted: boolean;
}

export const ConversationSchema = SchemaFactory.createForClass(Conversation);

// Create indexes for faster queries
ConversationSchema.index({ workspace: 1, status: 1 });
ConversationSchema.index({ assignedTo: 1 });
ConversationSchema.index({ lastActivityAt: -1 });
