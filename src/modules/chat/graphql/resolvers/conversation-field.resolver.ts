import { <PERSON><PERSON><PERSON>, <PERSON>sol<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Int } from "@nestjs/graphql";
import { toGlobalId } from "graphql-relay";
import { ConversationType } from "../types/conversation.types";
import { Conversation } from "../../entities/conversation.entity";
import { MessageType } from "../types/message.types";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { JwtPayload } from "src/modules/auth/interfaces/jwt-payload.interface";
import { UserType } from "@app/users/graphql/types/user.type";
import { UserLoader } from "src/modules/graphql/loaders/user.loader";
import { MessageLoader } from "src/modules/graphql/loaders/message.loader";
import { ContactLoader } from "src/modules/graphql/loaders/contact.loader";
import { Inject, UseGuards } from "@nestjs/common";
import { ChatServiceInterface } from "../../interfaces/chat-service.interface";
import { ContactType } from "src/modules/contact/graphql/types/contact.type";
import { GqlAuthGuard } from "src/modules/auth/guards/gql-auth.guard";

@Resolver(() => ConversationType)
export class ConversationFieldResolver {
  constructor(
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface,
    @Inject(UserLoader) private readonly userLoader: UserLoader,
    @Inject(MessageLoader) private readonly messageLoader: MessageLoader,
    @Inject(ContactLoader) private readonly contactLoader: ContactLoader
  ) {}

  @ResolveField(() => String)
  id(@Parent() item: Conversation) {
    return toGlobalId("Conversation", item.id);
  }

  @ResolveField(() => String)
  rawId(@Parent() item: Conversation) {
    return item.id;
  }

  //use ResolveField to query assignedTo if you user need to get assignedTo info
  //use DataLoader to fix N+1 queries, ex: you have 20 conversations, but you need one query users
  @ResolveField(() => UserType, { nullable: true })
  async assignedTo(@Parent() conv: ConversationType): Promise<UserType | null> {
    if (conv.assignedToId != null) {
      return this.userLoader.loadUserWithWorkspace({
        userId: conv.assignedToId,
        workspaceId: conv.workspaceId ?? "",
      }) as Promise<UserType>;
    } else {
      return null;
    }
  }

  @ResolveField(() => [UserType], { nullable: true })
  async participants(
    @Parent() conv: ConversationType
  ): Promise<UserType[] | null> {
    if (conv.participantsIds != null && conv.participantsIds.length > 0) {
      console.log({
        participantsIds: conv.participantsIds,
        workspaceId: conv.workspaceId,
      });
      const users = await Promise.all(
        conv.participantsIds.map(
          (id) =>
            this.userLoader.loadUserWithWorkspace({
              userId: id,
              workspaceId: conv.workspaceId ?? "",
            }) as Promise<UserType>
        )
      );

      return users;
    } else {
      return [];
    }
  }

  @ResolveField(() => Int, { nullable: true })
  @UseGuards(GqlAuthGuard)
  async unreadCount(
    @Parent() conv: ConversationType,
    @CurrentUser() user: JwtPayload
  ): Promise<number> {
    console.log({ user });
    if (conv.lastReadTimestamps != null) {
      const count = await this.chatService.getUnreadCount(conv.id, user.sub);
      return count;
    } else {
      return 0;
    }
  }

  @ResolveField(() => MessageType, { nullable: true })
  async latestMessage(
    @Parent() conv: ConversationType
  ): Promise<MessageType | null> {
    if (conv.latestMessageId != null) {
      return this.messageLoader.batchMessages.load(
        conv.latestMessageId
      ) as Promise<MessageType>;
    } else {
      return null;
    }
  }

  @ResolveField(() => ContactType, { nullable: true })
  async contact(@Parent() conv: ConversationType): Promise<ContactType | null> {
    if (conv.contactId != null) {
      return this.contactLoader.batchContacts.load(
        conv.contactId
      ) as Promise<ContactType>;
    } else {
      return null;
    }
  }
}
