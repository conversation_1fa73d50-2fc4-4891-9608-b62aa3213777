import { <PERSON><PERSON><PERSON>, ResolveField, Parent } from "@nestjs/graphql";
import { toGlobalId } from "graphql-relay";
import { ConversationType } from "../types/conversation.types";
import { Conversation } from "../../entities/conversation.entity";

@Resolver(() => ConversationType)
export class ConversationFieldResolver {
  constructor() {}

  @ResolveField(() => String)
  id(@Parent() item: Conversation) {
    return toGlobalId("Conversation", item.id);
  }

  @ResolveField(() => String)
  rawId(@Parent() item: Conversation) {
    return item.id;
  }
}
