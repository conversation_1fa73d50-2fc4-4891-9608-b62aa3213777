import { <PERSON><PERSON><PERSON>, ResolveField, Parent } from "@nestjs/graphql";
import { toGlobalId } from "graphql-relay";
import { MessageType } from "../types/message.types";
import { Message } from "../../entities/message.entity";

@Resolver(() => MessageType)
export class MessageFieldResolver {
  constructor() {}

  @ResolveField(() => String)
  id(@Parent() item: Message) {
    return toGlobalId("Message", item.id);
  }

  @ResolveField(() => String)
  rawId(@Parent() item: Message) {
    return item.id;
  }
}
