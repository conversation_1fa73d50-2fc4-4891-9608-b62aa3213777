import { <PERSON>sol<PERSON>, ResolveField, Parent } from "@nestjs/graphql";
import { toGlobalId } from "graphql-relay";
import { MessageType } from "../types/message.types";
import { Message } from "../../entities/message.entity";
import { Inject } from "@nestjs/common";
import { MessageLoader } from "src/modules/graphql/loaders/message.loader";

@Resolver(() => MessageType)
export class MessageFieldResolver {
  constructor(
    @Inject(MessageLoader) private readonly messageLoader: MessageLoader
  ) {}

  @ResolveField(() => String)
  id(@Parent() item: Message) {
    return toGlobalId("Message", item.id);
  }

  @ResolveField(() => String)
  rawId(@Parent() item: Message) {
    return item.id;
  }

  @ResolveField(() => MessageType, { nullable: true })
  async replyTo(@Parent() msg: MessageType): Promise<MessageType | null> {
    if (msg.replyToId != null) {
      return this.messageLoader.batchMessages.load(
        msg.replyToId
      ) as Promise<MessageType>;
    } else {
      return null;
    }
  }
}
