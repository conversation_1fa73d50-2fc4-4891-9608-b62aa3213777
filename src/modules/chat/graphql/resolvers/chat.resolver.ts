import { Resolver, Query, Args, ID } from "@nestjs/graphql";
import { Inject, UseGuards } from "@nestjs/common";
import { ChatServiceInterface } from "../../interfaces/chat-service.interface";
import {
  ConversationConnection,
  ConversationType,
} from "../types/conversation.types";
import { MessageConnection } from "../types/message.types";

import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { JwtPayload } from "src/modules/auth/interfaces/jwt-payload.interface";
import { GqlAuthGuard } from "src/modules/auth/guards/gql-auth.guard";

@Resolver(() => ConversationType)
export class ChatResolver {
  constructor(
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface
  ) {}

  @Query(() => ConversationConnection)
  @UseGuards(GqlAuthGuard)
  async conversations(
    @CurrentUser() user: JwtPayload,
    @Args("assignedToMe", { nullable: true }) assignedToMe?: boolean,
    @Args("first", { nullable: true }) first?: number,
    @Args("after", { nullable: true }) after?: string,
    @Args("last", { nullable: true }) last?: number,
    @Args("before", { nullable: true }) before?: string
  ): Promise<ConversationConnection> {
    return this.chatService.getConversations(
      user.workspaceId,
      {
        first,
        after,
        last,
        before,
      },
      assignedToMe == true ? user.sub : undefined
    );
  }

  @Query(() => MessageConnection)
  async messages(
    @Args("conversationId", { type: () => ID }) conversationId: string,
    @Args("first", { nullable: true }) first?: number,
    @Args("after", { nullable: true }) after?: string,
    @Args("last", { nullable: true }) last?: number,
    @Args("before", { nullable: true }) before?: string
  ): Promise<MessageConnection> {
    return this.chatService.getMessages(conversationId, {
      first,
      after,
      last,
      before,
    });
  }
}
