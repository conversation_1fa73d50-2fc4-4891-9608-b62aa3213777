import {
  Field,
  ObjectType,
  ID,
  registerEnumType,
  GraphQLISODateTime,
} from "@nestjs/graphql";

import {
  MessageSender,
  MessageStatus,
  MessageType as MessageTypeEnum,
} from "../../schemas/message.schema";
import GraphQLJSON from "graphql-type-json";
import { Node } from "src/modules/graphql/types/node.interface";
import { ConnectionType } from "src/modules/graphql/types/connection.type";
import { UserType } from "@app/users/graphql/types/user.type";

// Register enums for GraphQL
registerEnumType(MessageSender, {
  name: "MessageSender",
  description: "Type of message sender",
});

registerEnumType(MessageStatus, {
  name: "MessageStatus",
  description: "Status of a message",
});

registerEnumType(MessageTypeEnum, {
  name: "MessageTypeEnum",
  description: "Type of message content",
});

@ObjectType("Message", { implements: [Node] })
export class MessageType implements Node {
  @Field(() => ID)
  id: string;

  @Field(() => ID)
  conversationId: string;

  @Field(() => String)
  content: string;

  @Field(() => MessageSender)
  sender: MessageSender;

  @Field(() => UserType, { nullable: true })
  user?: UserType;

  // Hidden field to store the user ID for the resolver
  userId?: string;

  @Field(() => MessageType, { nullable: true })
  replyTo?: MessageType;

  // Hidden field to store the replyTo ID for the resolver
  replyToId?: string;

  @Field(() => MessageTypeEnum, { nullable: true })
  type?: MessageTypeEnum;

  @Field(() => MessageStatus)
  status: MessageStatus;

  @Field(() => GraphQLJSON, { nullable: true })
  metadata?: Record<string, any>;

  @Field(() => GraphQLISODateTime)
  createdAt: Date;

  @Field(() => GraphQLISODateTime)
  updatedAt: Date;
}

@ObjectType()
export class MessageConnection extends ConnectionType(MessageType) {}
