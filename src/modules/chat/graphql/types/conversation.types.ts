import {
  Field,
  ObjectType,
  registerEnumType,
  ID,
  GraphQLISODateTime,
  Int,
} from "@nestjs/graphql";

import GraphQLJSON from "graphql-type-json";
import { Node } from "src/modules/graphql/types/node.interface";
import { ConnectionType } from "src/modules/graphql/types/connection.type";
import { MessageType } from "./message.types";
import { UserType } from "@app/users/graphql/types/user.type";
import { ContactType } from "src/modules/contact/graphql/types/contact.type";

@ObjectType("Conversation", { implements: [Node] })
export class ConversationType implements Node {
  @Field(() => ID)
  id: string;

  // Hidden field to store the workspace ID for the resolver
  workspaceId?: string;

  @Field(() => ContactType, { nullable: true })
  contact?: ContactType;

  // Hidden field to store the user ID for the resolver
  contactId?: string;

  @Field(() => UserType, { nullable: true })
  assignedTo?: UserType;

  // Hidden field to store the user ID for the resolver
  assignedToId?: string;

  @Field(() => [UserType], { nullable: true })
  participants?: UserType[];

  // Hidden field to store the user ID for the resolver
  participantsIds?: string[];

  @Field(() => Boolean, { nullable: true })
  resolved?: boolean;

  @Field(() => [String], { nullable: true })
  segments?: string[];

  @Field(() => String, { nullable: true })
  subject?: string;

  @Field(() => GraphQLJSON, { nullable: true })
  metadata?: Record<string, any>;

  @Field(() => GraphQLISODateTime, { nullable: true })
  createdAt?: Date;

  @Field(() => GraphQLISODateTime, { nullable: true })
  updatedAt?: Date;

  @Field(() => GraphQLISODateTime, { nullable: true })
  lastActivityAt?: Date;

  @Field(() => GraphQLISODateTime, { nullable: true })
  closedAt?: Date;

  @Field(() => Int, { nullable: true })
  unreadCount?: number;

  // Hidden field to store the lastReadTimestamps for the resolver
  lastReadTimestamps?: Record<string, any>;

  @Field(() => MessageType, { nullable: true })
  latestMessage?: MessageType;

  // Hidden field to store the message ID for the resolver
  latestMessageId?: string;
}

@ObjectType()
export class ConversationConnection extends ConnectionType(ConversationType) {}
