import { CreateMessageDto } from "../dto/send-message.dto";
import { ConnectionArgs } from "src/modules/graphql/types/connection.args";
import { ConversationConnection } from "../graphql/types/conversation.types";
import { MessageConnection } from "../graphql/types/message.types";
import { ConversationDocument } from "../entities/conversation.entity";
import { MessageDocument } from "../entities/message.entity";
import { UpdateConversationDto } from "../dto/update-conversation.dto";

export interface ChatServiceInterface {
  sendMessage(body: CreateMessageDto): Promise<MessageDocument>;

  /**
   * Get a conversation by ID without including latest message, contact, etc.
   * @param conversationId ID of the conversation
   * @returns The conversation if found
   */
  getConversationById(conversationId: string): Promise<ConversationDocument>;

  /**
   * Get conversation by guest ID and workspace ID
   * @param guestId Guest ID
   * @param workspaceId Workspace ID
   */
  getConversationByContactId(
    contactId: string,
    workspaceId: string
  ): Promise<ConversationDocument>;

  getConversationsByUser(
    userId: string,
    workspaceId: string
  ): Promise<ConversationDocument[]>;

  getConversations(
    workspaceId: string,
    args: ConnectionArgs,
    assignedToId?: string
  ): Promise<ConversationConnection>;

  getConversationsByEmailContact(
    email: string,
    limit: number
  ): Promise<ConversationDocument[]>;

  getMessages(
    conversationId: string,
    args: ConnectionArgs
  ): Promise<MessageConnection>;

  findMessagesByIds(ids: string[]): Promise<MessageDocument[]>;

  updateUserOnlineStatus(
    userId: string,
    workspaceId: string,
    status: boolean
  ): Promise<void>;
  updateContactOnlineStatus(
    contactId: string,
    workspaceId: string,
    status: boolean
  ): Promise<void>;

  markMessagesAsRead(conversationId: string, userId: string): Promise<void>;
  getUnreadCount(conversationId: string, userId: string): Promise<number>;

  updateConversation(
    conversationId: string,
    updateDto: UpdateConversationDto
  ): Promise<ConversationDocument>;

  removeConversation(id: string, userId: string): Promise<boolean>;
}
