import { CanActivate, ExecutionContext, Injectable } from "@nestjs/common";
import { JwtService } from "@nestjs/jwt";
import { WsException } from "@nestjs/websockets";
import { Socket } from "socket.io";

@Injectable()
export class WsJwtGuard implements CanActivate {
  constructor(private readonly jwtService: JwtService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    try {
      const client: Socket = context.switchToWs().getClient();
      const token = client.handshake.auth.token as string;

      if (!token) {
        throw new WsException("Missing authentication token");
      }

      // Verify the JWT token
      const payload = this.jwtService.verify(token);

      // Attach user to client
      (client as any).user = {
        id: payload.sub,
        email: payload.email,
      };

      return true;
    } catch (error) {
      throw new WsException("Unauthorized access");
    }
  }
}
