import {
  Body,
  Controller,
  Delete,
  Inject,
  Param,
  Put,
  Request,
} from "@nestjs/common";
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from "@nestjs/swagger";
import { ChatServiceInterface } from "../interfaces/chat-service.interface";
import { AnalyticsService } from "src/common/services/analytics.service";
import { ContactServiceInterface } from "src/modules/contact";
import { UpdateConversationDto } from "../dto/update-conversation.dto";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { Conversation } from "../entities/conversation.entity";
import { EventEmitter2 } from "@nestjs/event-emitter";
import { ResolvedConversationEventDto } from "@app/shared/events/dto/resolved-conversation.event.dto";
import { RESOLVED_CONVERSATION_EVENT } from "@app/shared/events/events.constants";
import { SendMessageDto } from "../dto/send-message.dto";
import { MessageType } from "../schemas/message.schema";

@ApiTags("Chat")
@Controller("chat")
export class ChatController {
  constructor(
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface,
    private readonly analyticsService: AnalyticsService,
    @Inject("ContactServiceInterface")
    private readonly contactService: ContactServiceInterface,
    private eventEmitter: EventEmitter2
  ) {}

  @Put("conversations/:id")
  @ApiOperation({ summary: "Update a conversation" })
  @ApiParam({ name: "id", description: "Conversation ID" })
  @ApiResponse({
    status: 200,
    description: "Conversation updated successfully",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Conversation not found" })
  async update(
    @Param("id") id: string,
    @Body() updateDto: UpdateConversationDto,
    @Request() req: RequestWithUser
  ): Promise<Conversation> {
    try {
      const conv = await this.chatService.updateConversation(id, updateDto);
      // TODO: maybe implement some logic when update assignedTo or participants
      // like notify the new assignee or participants
      if (updateDto.resolved) {
        this.eventEmitter.emit(
          RESOLVED_CONVERSATION_EVENT,
          new ResolvedConversationEventDto({
            conversationId: id,
            message: {
              content: updateDto.resolved
                ? "Conversation resolved"
                : "Conversation reopened",
              type: MessageType.RESOLVED,
            },
            resolvedBy: req.user.sub,
            resolved: updateDto.resolved,
          })
        );
      }
      return new Conversation(conv);
    } catch (error) {
      throw new Error(error);
    }
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a conversation" })
  @ApiParam({ name: "id", description: "Conversation ID" })
  @ApiResponse({
    status: 200,
    description: "Conversation deleted successfully",
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Conversation not found" })
  remove(
    @Param("id") id: string,
    @Request() req: RequestWithUser
  ): Promise<boolean> {
    return this.chatService.removeConversation(id, req.user.sub);
  }
}
