import { Document, Types } from "mongoose";
import { User, UserDocument } from "../../users/entities/user.entity";
import { Message, MessageDocument } from "./message.entity";
import { ConversationType } from "../graphql/types/conversation.types";
import {
  BaseDocument,
  BaseEntity,
} from "src/modules/common/entities/base.entity";
import { Contact, ContactDocument } from "src/modules/contact";
import {
  Workspace,
  WorkspaceDocument,
} from "src/modules/workspace/entities/workspace.entity";

abstract class BaseConversation extends BaseEntity {
  workspace?: WorkspaceDocument | Workspace | null;
  workspaceId?: string;
  contact?: ContactDocument | Contact | null;
  contactId?: string;
  assignedTo?: UserDocument | User | null;
  assignedToId?: string;
  participants?: UserDocument[] | User[] | null;
  participantsIds?: string[];
  resolved: boolean;
  segments: string[];
  subject?: string;
  metadata?: Record<string, any>;
  lastActivityAt?: Date;
  closedAt?: Date;
  lastReadTimestamps?: Record<string, any>;
  latestMessage?: MessageDocument | Message | null;
  latestMessageId?: string;
}

export interface ConversationDocument extends BaseDocument<BaseConversation> {
  _id: Types.ObjectId;
}

export class Conversation extends BaseConversation {
  constructor(conversationDoc?: ConversationDocument) {
    super();
    if (conversationDoc) {
      super.assignFields(conversationDoc);

      this.workspaceId = (
        conversationDoc.workspace as WorkspaceDocument
      )?._id?.toString();
      this.contactId = (
        conversationDoc.contact as ContactDocument
      )?._id?.toString();
      this.assignedToId = (
        conversationDoc.assignedTo as UserDocument
      )?._id?.toString();
      this.participantsIds = (
        conversationDoc.participants as UserDocument[]
      )?.map((item) => item._id.toString());
      this.resolved = conversationDoc.resolved;
      this.segments = conversationDoc.segments;
      this.subject = conversationDoc.subject;
      this.metadata = conversationDoc.metadata;
      this.lastActivityAt = conversationDoc.lastActivityAt;
      this.closedAt = conversationDoc.closedAt;
      this.lastReadTimestamps = conversationDoc.lastReadTimestamps;
      this.latestMessageId = (
        conversationDoc.latestMessage as MessageDocument
      )?._id?.toString();
      this.latestMessage = conversationDoc.latestMessage;
    }
  }

  toGraphQL(): ConversationType {
    const conversationType = new ConversationType();
    conversationType.id = this.id;
    conversationType.contactId = this.contactId;
    conversationType.assignedToId = this.assignedToId;
    conversationType.participantsIds = this.participantsIds;
    conversationType.resolved = this.resolved;
    conversationType.segments = this.segments;
    conversationType.subject = this.subject;
    conversationType.metadata = this.metadata;
    conversationType.lastActivityAt = this.lastActivityAt;
    conversationType.closedAt = this.closedAt;
    conversationType.lastReadTimestamps = this.lastReadTimestamps;
    conversationType.latestMessageId = this.latestMessageId;
    conversationType.createdAt = this.createdAt;
    conversationType.updatedAt = this.updatedAt;
    return conversationType;
  }
}
