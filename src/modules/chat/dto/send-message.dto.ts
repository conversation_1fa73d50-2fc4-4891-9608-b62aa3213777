import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsO<PERSON>,
} from "class-validator";
import { MessageSender, MessageType } from "../schemas/message.schema";

export class SendMessageDto {
  @IsString()
  @IsNotEmpty({ message: "Message content is required" })
  content: string;

  @IsEnum(MessageType, { message: "Invalid message type" })
  type: MessageType;

  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @IsString()
  @IsOptional()
  replyToId?: string;
}

export class CreateMessageDto extends SendMessageDto {
  @IsString()
  @IsNotEmpty({ message: "Conversation ID is required" })
  conversationId: string;

  @IsString()
  @IsOptional()
  userId?: string;

  @IsEnum(MessageSender, { message: "Invalid message sender" })
  sender: MessageSender;
}
