import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsObject,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
  Validate,
} from "class-validator";
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { User } from "../../users/schemas/user.schema";

@ValidatorConstraint({ name: "IsUserExists", async: true })
@Injectable()
export class IsUserExistsConstraint implements ValidatorConstraintInterface {
  constructor(@InjectModel(User.name) private userModel: Model<User>) {}

  async validate(userId: string): Promise<boolean> {
    if (!userId) return true; // Let @IsOptional handle empty values

    try {
      const user = await this.userModel.findById(userId).exec();
      return !!user;
    } catch {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments): string {
    return `User with ID "${args.value}" does not exist`;
  }
}

// Custom decorator
export function IsUserExists(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsUserExistsConstraint,
    });
  };
}

export class UpdateConversationDto {
  @IsString()
  @IsOptional()
  latestMessageId?: string;

  @ApiProperty({
    description: "Whether the conversation is resolved",
    required: false,
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  resolved?: boolean;

  @ApiProperty({
    description: "The ID of the user to assign the conversation to",
    required: false,
    example: "1234567890",
  })
  @IsString()
  @IsOptional()
  @Validate(IsUserExistsConstraint, {
    message: "The assigned user does not exist",
  })
  assignedToId?: string;

  @ApiProperty({
    description: "The IDs of the users to add as participants",
    required: false,
    example: ["1234567890", "0987654321"],
  })
  @IsArray({ message: "Participants must be an array" })
  @IsString({ each: true })
  @IsOptional()
  @Validate(IsUserExistsConstraint, {
    each: true,
    message: "One or more participant users do not exist",
  })
  participantsIds?: string[];

  @ApiProperty({
    description: "The segments the conversation belongs to",
    required: false,
    example: ["VIP", "Enterprise"],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  segments?: string[];

  @ApiProperty({
    description: "Additional metadata for the contact",
    required: false,
    example: {
      source: "Website",
      lastPurchase: "2024-01-01",
    },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
