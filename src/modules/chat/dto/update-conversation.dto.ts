import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsOptional,
  IsBoolean,
  IsArray,
  IsObject,
} from "class-validator";
import { IsUserExists } from "../../../common/validators";

export class UpdateConversationDto {
  @IsString()
  @IsOptional()
  latestMessageId?: string;

  @ApiProperty({
    description: "Whether the conversation is resolved",
    required: false,
    example: true,
  })
  @IsBoolean()
  @IsOptional()
  resolved?: boolean;

  @ApiProperty({
    description: "The ID of the user to assign the conversation to",
    required: false,
    example: "1234567890",
  })
  @IsString()
  @IsOptional()
  @IsUserExists({
    message: "The assigned user does not exist",
  })
  assignedToId?: string;

  @ApiProperty({
    description: "The IDs of the users to add as participants",
    required: false,
    example: ["1234567890", "0987654321"],
  })
  @IsArray({ message: "Participants must be an array" })
  @IsString({ each: true })
  @IsOptional()
  @IsUserExists({
    each: true,
    message: "One or more participant users do not exist",
  })
  participantsIds?: string[];

  @ApiProperty({
    description: "The segments the conversation belongs to",
    required: false,
    example: ["VIP", "Enterprise"],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  segments?: string[];

  @ApiProperty({
    description: "Additional metadata for the contact",
    required: false,
    example: {
      source: "Website",
      lastPurchase: "2024-01-01",
    },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}
