/* eslint-disable @typescript-eslint/no-unsafe-assignment */
/* eslint-disable @typescript-eslint/no-unsafe-member-access */

import {
  Injectable,
  NotFoundException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { ChatServiceInterface } from "../interfaces/chat-service.interface";
import { CreateMessageDto } from "../dto/send-message.dto";
import { fromGlobalId, toGlobalId } from "graphql-relay";
import { ConnectionArgs } from "src/modules/graphql/types/connection.args";
import { ConversationConnection } from "../graphql/types/conversation.types";
import { MessageConnection } from "../graphql/types/message.types";
import {
  Conversation,
  ConversationDocument,
} from "../entities/conversation.entity";
import { Message, MessageDocument } from "../entities/message.entity";
import { UserStatusRedisService } from "src/services/redis/user-status.redis.service";
import { UpdateConversationDto } from "../dto/update-conversation.dto";
import { ContactServiceInterface } from "src/modules/contact";

@Injectable()
export class ChatService implements ChatServiceInterface {
  constructor(
    @InjectModel(Conversation.name)
    private conversationModel: Model<Conversation>,
    @InjectModel(Message.name)
    private messageModel: Model<Message>,
    private readonly userStatusRedisService: UserStatusRedisService,
    @Inject("ContactServiceInterface")
    private readonly contactService: ContactServiceInterface
  ) {}

  async updateUserOnlineStatus(
    userId: string,
    workspaceId: string,
    status: boolean
  ): Promise<void> {
    // Update status in Redis instead of database
    if (status) {
      await this.userStatusRedisService.setUserOnline(userId, workspaceId);
    } else {
      await this.userStatusRedisService.setUserOffline(userId, workspaceId);
    }
  }

  async updateContactOnlineStatus(
    contactId: string,
    workspaceId: string,
    status: boolean
  ): Promise<void> {
    if (status) {
      await this.userStatusRedisService.setContactOnline(
        contactId,
        workspaceId
      );
    } else {
      await this.userStatusRedisService.setContactOffline(
        contactId,
        workspaceId
      );
    }
  }

  async sendMessage(body: CreateMessageDto): Promise<MessageDocument> {
    try {
      // Find the conversation and verify it belongs to this guest
      const conversation = await this.conversationModel.findOne({
        _id: new Types.ObjectId(body.conversationId),
      });

      if (!conversation) {
        throw new NotFoundException("Conversation not found");
      }

      const message = new this.messageModel({
        ...body,
        conversation: body.conversationId,
        user: body.userId,
        replyTo: body.replyToId,
      });

      const savedMessage = await message.save();

      return savedMessage;
    } catch (error: unknown) {
      if (
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to send message: ${error instanceof Error ? error.message : "Unknown error"}`
      );
    }
  }

  async getConversationById(
    conversationId: string
  ): Promise<ConversationDocument> {
    const conversation = await this.conversationModel
      .findById(conversationId)
      .exec();

    if (!conversation) {
      throw new NotFoundException("Conversation not found");
    }

    return conversation;
  }

  async getConversationByContactId(
    contactId: string,
    workspaceId: string
  ): Promise<ConversationDocument> {
    let conversation = await this.conversationModel
      .findOne({
        contact: contactId,
        workspace: workspaceId,
      })
      .exec();

    if (!conversation) {
      conversation = await this.conversationModel.create({
        workspace: workspaceId,
        contact: contactId,
      });
    }

    return conversation;
  }

  async getConversations(
    workspaceId: string,
    args: ConnectionArgs,
    assignedToId?: string
  ): Promise<ConversationConnection> {
    const { first, after, before, last } = args;

    if (before || last) {
      throw new Error("not supported");
    }

    const limit = first ?? 10;

    const query: any = {
      workspace: new Types.ObjectId(workspaceId),
      deleted: false,
    };
    if (assignedToId) {
      query.assignedTo = new Types.ObjectId(assignedToId);
    }

    if (after) {
      const afterConversation = await this.conversationModel.findById(
        fromGlobalId(after).id
      );
      if (afterConversation) {
        query.updatedAt = { $lt: afterConversation.updatedAt };
      }
    }

    const sort: any = { updatedAt: -1 };

    let conversations = await this.conversationModel
      .find(query)
      .sort(sort)
      .limit(limit + 1); // +1 to check hasNext/hasPrevious

    const hasExtraItem = conversations.length > limit;
    if (hasExtraItem) conversations = conversations.slice(0, limit);

    const edges = conversations.map((conv) => ({
      node: new Conversation(conv).toGraphQL(),
      cursor: toGlobalId("Conversation", conv._id.toString()),
    }));

    const startCursor = edges[0]?.cursor;
    const endCursor = edges[edges.length - 1]?.cursor;

    return {
      edges,
      pageInfo: {
        hasNextPage: hasExtraItem,
        hasPreviousPage: after !== null,
        startCursor,
        endCursor,
      },
    };
  }

  async getMessages(
    conversationId: string,
    args: ConnectionArgs
  ): Promise<MessageConnection> {
    const { first, after, before, last } = args;

    if (before || last) {
      throw new Error("not supported");
    }

    const limit = first ?? 10;
    const query: any = { conversation: conversationId };

    if (after) {
      query._id = { $lt: new Types.ObjectId(fromGlobalId(after).id) };
    }

    const sort: any = { _id: -1 };

    let messages = await this.messageModel
      .find(query)
      .sort(sort)
      .limit(limit + 1); // +1 to check hasNext/hasPrevious

    const hasExtraItem = messages.length > limit;
    if (hasExtraItem) messages = messages.slice(0, limit);

    const edges = messages.map((msg) => ({
      node: new Message(msg).toGraphQL(),
      cursor: toGlobalId("Message", msg._id.toString()),
    }));

    const startCursor = edges[0]?.cursor;
    const endCursor = edges[edges.length - 1]?.cursor;

    return {
      edges,
      pageInfo: {
        hasNextPage: hasExtraItem,
        hasPreviousPage: after !== null,
        startCursor,
        endCursor,
      },
    };
  }

  async findMessagesByIds(ids: string[]): Promise<MessageDocument[]> {
    return this.messageModel.find({ _id: { $in: ids } }).exec();
  }

  async getConversationsByUser(
    userId: string,
    workspaceId: string
  ): Promise<ConversationDocument[]> {
    return this.conversationModel
      .find({ workspace: workspaceId, deleted: false })
      .exec();
  }

  async markMessagesAsRead(
    conversationId: string,
    userId: string
  ): Promise<void> {
    await this.conversationModel.findByIdAndUpdate(conversationId, {
      $set: { [`lastReadTimestamps.${userId}`]: new Date() },
    });
  }

  async getUnreadCount(
    conversationId: string,
    userId: string
  ): Promise<number> {
    const conv = await this.conversationModel.findById(conversationId).lean();
    if (!conv) return 0;

    const lastRead =
      conv.lastReadTimestamps instanceof Map
        ? conv.lastReadTimestamps.get(userId)
        : conv.lastReadTimestamps?.[userId] || new Date(0);

    const count = await this.messageModel.countDocuments({
      conversation: new Types.ObjectId(conversationId),
      createdAt: { $gt: lastRead },
      user: { $ne: new Types.ObjectId(userId) },
    });

    return count;
  }

  async getConversationsByEmailContact(
    email: string,
    limit: number
  ): Promise<ConversationDocument[]> {
    const contacts = await this.contactService.findContactsByEmail(email);
    if (!contacts || contacts.length === 0) {
      return [];
    }

    // Extract contact IDs
    const contactIds = contacts.map((contact) => contact._id);

    // Find conversations for these contacts
    return this.conversationModel
      .find({
        contact: { $in: contactIds },
        latestMessage: { $exists: true },
        deleted: false,
      })
      .sort({ updatedAt: -1 })
      .limit(limit)
      .exec();
  }

  async updateConversation(
    conversationId: string,
    updateDto: UpdateConversationDto
  ): Promise<ConversationDocument> {
    const body: any = { ...updateDto };
    if (updateDto.latestMessageId) {
      body.latestMessage = new Types.ObjectId(updateDto.latestMessageId);
      body.lastActivityAt = new Date();
      delete body.latestMessageId;
    }

    if (updateDto.assignedToId) {
      body.assignedTo = new Types.ObjectId(updateDto.assignedToId);
      delete body.assignedToId;
    }

    if (updateDto.participantsIds) {
      body.participants = updateDto.participantsIds.map(
        (id) => new Types.ObjectId(id)
      );
      delete body.participantsIds;
    }

    const updatedConversation = await this.conversationModel
      .findByIdAndUpdate(conversationId, body, { new: true })
      .exec();
    if (!updatedConversation) {
      throw new NotFoundException("Conversation not found");
    }
    return updatedConversation;
  }

  async removeConversation(id: string, userId: string): Promise<boolean> {
    const result = await this.conversationModel
      .updateOne({ _id: id }, { deleted: true })
      .exec();
    return result !== null;
  }
}
