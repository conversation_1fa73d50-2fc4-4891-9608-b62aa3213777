import { forwardR<PERSON>, Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { JwtModule } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { RedisModule as IoRedisModule } from "@nestjs-modules/ioredis";
import { WorkspaceModule } from "../workspace/workspace.module";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { RedisModule } from "../../services/redis/redis.module";

import {
  Conversation,
  ConversationSchema,
} from "./schemas/conversation.schema";
import { Message, MessageSchema } from "./schemas/message.schema";

import { ChatService } from "./services/chat.service";
import { ChatGateway } from "./gateways/chat.gateway";
import { WsJwtGuard } from "./guards/ws-jwt.guard";
import { ChatResolver } from "./graphql/resolvers/chat.resolver";
import { UsersModule } from "@app/users";
import { LoaderModule } from "../graphql/loaders/loader.module";
import { ContactModule } from "../contact";
import { ChatController } from "./controllers/chat.controller";
import { CommonModule } from "src/common/common.module";
import { MessageFieldResolver } from "./graphql/resolvers/message-field.resolver";
import { ConversationFieldResolver } from "./graphql/resolvers/conversation-field.resolver";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Conversation.name, schema: ConversationSchema },
      { name: Message.name, schema: MessageSchema },
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>("JWT_SECRET"),
        signOptions: { expiresIn: configService.get<string>("JWT_EXPIRATION") },
      }),
    }),
    // Redis for scaling WebSockets and cache
    IoRedisModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        config: {
          url: configService.get<string>("REDIS_URL"),
          keyPrefix: "chat:",
        },
      }),
    }),
    forwardRef(() => WorkspaceModule),
    forwardRef(() => UsersModule),
    EventEmitterModule.forRoot(),
    forwardRef(() => LoaderModule),
    RedisModule,
    ContactModule,
    CommonModule,
  ],
  controllers: [ChatController],
  providers: [
    {
      provide: "ChatServiceInterface",
      useClass: ChatService,
    },
    ChatGateway,
    WsJwtGuard,
    ChatResolver,
    MessageFieldResolver,
    ConversationFieldResolver,
  ],
  exports: [
    "ChatServiceInterface",
    ChatResolver,
    MessageFieldResolver,
    ConversationFieldResolver,
  ],
})
export class ChatModule {}
