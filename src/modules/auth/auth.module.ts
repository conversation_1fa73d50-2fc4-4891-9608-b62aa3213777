import { forwardRef, Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { JwtModule } from "@nestjs/jwt";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { PassportModule } from "@nestjs/passport";
import { AuthController } from "./controllers/auth.controller";
import { AuthService } from "./services/auth.service";
import {
  Registration,
  RegistrationSchema,
} from "./schemas/registration.schema";
import {
  VerificationCode,
  VerificationCodeSchema,
} from "./schemas/verification-code.schema";
import { JwtStrategy } from "./strategies/jwt.strategy";
import { UsersModule } from "../users";
import { WorkspaceModule } from "../workspace/workspace.module";
import { MailModule } from "../../mail/mail.module";
import { JwtAuthGuard } from "./guards/jwt-auth.guard";
import {
  PasswordResetToken,
  PasswordResetTokenSchema,
} from "./schemas/password-reset-token.schema";
import { GoogleStrategy } from "./strategies/google.strategy";
import { OptionalJwtAuthGuard } from "./guards/optional-jwt-auth.guard";
import { CommonModule } from "src/common/common.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Registration.name, schema: RegistrationSchema },
      { name: VerificationCode.name, schema: VerificationCodeSchema },
      { name: PasswordResetToken.name, schema: PasswordResetTokenSchema },
    ]),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (configService: ConfigService) => ({
        secret: configService.get<string>("JWT_SECRET"),
        signOptions: { expiresIn: configService.get<string>("JWT_EXPIRATION") },
      }),
      inject: [ConfigService],
    }),
    PassportModule,
    forwardRef(() => UsersModule),
    forwardRef(() => WorkspaceModule),
    MailModule,
    CommonModule,
  ],
  controllers: [AuthController],
  providers: [
    {
      provide: "AuthServiceInterface",
      useClass: AuthService,
    },
    JwtStrategy,
    GoogleStrategy,
    JwtAuthGuard,
    OptionalJwtAuthGuard,
  ],
  exports: ["AuthServiceInterface", JwtAuthGuard, OptionalJwtAuthGuard],
})
export class AuthModule {}
