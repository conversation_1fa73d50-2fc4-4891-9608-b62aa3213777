import { Injectable, Inject, forwardRef } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import * as bcrypt from "bcrypt";
import * as crypto from "crypto";
import { JwtService } from "@nestjs/jwt";
import { ConfigService } from "@nestjs/config";
import { AuthServiceInterface } from "../interfaces/auth-service.interface";
import { Registration } from "../schemas/registration.schema";
import { VerificationCode } from "../schemas/verification-code.schema";
import { RegisterDto } from "../dto/register.dto";
import { VerifyCodeDto } from "../dto/verify-code.dto";
import { EndRegisterDto } from "../dto/end-register.dto";
import { UserStatus } from "../../users/schemas/user.schema";
import { AnalyticsData } from "src/common/services/analytics.service";
import { I18nHelper } from "../../../common/utils/i18n.helper";
import { LoginDto } from "../dto/login.dto";
import { WorkspaceServiceInterface } from "../../workspace/interfaces/workspace-service.interface";
import { MailService } from "../../../mail";
import { PasswordResetToken } from "../schemas/password-reset-token.schema";
import { JwtPayload } from "../interfaces/jwt-payload.interface";
import { WorkspaceMemberServiceInterface } from "src/modules/workspace/interfaces/workspace-member-service.interface";
import { UserDocument, UserServiceInterface } from "@app/users";
import { SetWebsiteDto } from "../dto/set-website.dto";
import { InviteTeamDto } from "../dto/invite-team.dto";
import { WorkspaceMemberRole } from "src/modules/workspace/schemas/workspace-member.schema";
import { GoogleUser } from "../interfaces/google-payload.interface";

@Injectable()
export class AuthService implements AuthServiceInterface {
  constructor(
    @InjectModel(Registration.name)
    private registrationModel: Model<Registration>,
    @InjectModel(VerificationCode.name)
    private verificationCodeModel: Model<VerificationCode>,
    @Inject(forwardRef(() => "UserServiceInterface"))
    private readonly userService: UserServiceInterface,
    private jwtService: JwtService,
    private configService: ConfigService,
    @Inject("WorkspaceServiceInterface")
    private readonly workspaceService: WorkspaceServiceInterface,
    @Inject("WorkspaceMemberServiceInterface")
    private readonly workspaceMemberService: WorkspaceMemberServiceInterface,
    private readonly mailService: MailService,
    @InjectModel(PasswordResetToken.name)
    private passwordResetTokenModel: Model<PasswordResetToken>
  ) {}

  async startRegistration(
    registerDto: RegisterDto,
    analyticsData?: AnalyticsData
  ): Promise<{ message: string; registrationId: string }> {
    const { email, password, phoneNumber } = registerDto;
    //if name is not provided, use email as name
    const name = registerDto.name || email.split("@")[0];

    // Check if email already exists in the system
    const existingUser = await this.userService.findByEmail(email);
    if (existingUser) {
      if (existingUser.status !== UserStatus.PENDING) {
        throw I18nHelper.conflict("errors.auth.email_exists");
      }
    }

    // Check for an existing registration process
    let registration = await this.registrationModel.findOne({ email }).exec();

    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 10);

    if (registration) {
      // Update existing registration
      registration.name = name;
      registration.password = hashedPassword;
      registration.phoneNumber = phoneNumber;
      registration.registrationStep = 1;
      registration.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // Expires after 24h
      await registration.save();
    } else {
      // Create new registration
      registration = await this.registrationModel.create({
        name,
        email,
        password: hashedPassword,
        phoneNumber,
        registrationStep: 1,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // Expires after 24h
      });
    }

    // Send verification code with analytics data
    await this.sendVerificationCode(email, analyticsData);

    return {
      message:
        "Registration process started successfully. Verification code has been sent to your email.",
      registrationId: registration._id.toString(),
    };
  }

  async sendVerificationCode(
    email: string,
    analyticsData?: AnalyticsData
  ): Promise<{ message: string }> {
    // Generate random 6-digit verification code
    const code = Math.floor(100000 + Math.random() * 900000).toString();

    // Invalidate any existing unused codes
    await this.verificationCodeModel
      .updateMany({ email, used: false }, { used: true })
      .exec();

    // Create new verification code
    await this.verificationCodeModel.create({
      email,
      code,
      expiresAt: new Date(Date.now() + 10 * 60 * 1000), // Expires after 10 minutes
      used: false,
    });
    try {
      await this.mailService.sendVerificationCode(email, code, analyticsData);
    } catch (error) {
      // Log the error but don't fail the request
      console.error(`Failed to send verification email to ${email}:`, error);
    }

    return { message: "Verification code has been sent to your email" };
  }

  async verifyCode(
    verifyDto: VerifyCodeDto
  ): Promise<{ message: string; token: string }> {
    const { email, code } = verifyDto;

    // Check registration process
    const registration = await this.registrationModel.findOne({ email }).exec();
    if (!registration) {
      throw I18nHelper.notFound("errors.auth.not_found");
    }

    // First check if the code exists for this email
    const verificationCodeRecord = await this.verificationCodeModel
      .findOne({
        email,
        code,
      })
      .exec();

    // If no code exists, it's an invalid code
    if (!verificationCodeRecord) {
      throw I18nHelper.badRequest("errors.auth.invalid_code");
    }

    // If the code has been used already
    if (verificationCodeRecord.used) {
      throw I18nHelper.badRequest("errors.auth.used_code");
    }

    // Check if the code has expired
    if (verificationCodeRecord.expiresAt < new Date()) {
      throw I18nHelper.badRequest("errors.auth.expired_code");
    }

    const firstName = registration.name.split(" ")[0] || registration.name;
    const lastName = registration.name.split(" ").slice(1).join(" ") || "";

    let userId: string;

    // Check if a user with this email already exists
    const existingUser = await this.userService.findByEmail(email);
    if (existingUser) {
      userId = existingUser._id.toString();
      if (existingUser.status !== UserStatus.PENDING) {
        throw I18nHelper.conflict("errors.auth.email_exists");
      } else {
        await this.userService.updateOne(existingUser._id.toString(), {
          firstName,
          lastName,
          status: UserStatus.ACTIVE,
          password: registration.password,
        });
      }
    } else {
      const newUser = await this.userService.createUser(
        firstName,
        lastName,
        registration.email,
        registration.password
      );
      await this.userService.updateOne(newUser._id.toString(), {
        status: UserStatus.ACTIVE,
      });
      userId = newUser._id.toString();
    }

    const workspace = await this.workspaceService.createDefaultWorkspace(
      userId,
      registration.name
    );

    //update registration with workspace and owner
    await this.registrationModel.updateOne(
      { _id: registration._id },
      {
        workspace: workspace._id,
        owner: userId,
        isEmailVerified: true,
        registrationStep: 2,
      }
    );

    // Code is valid, mark it as used
    verificationCodeRecord.used = true;
    await verificationCodeRecord.save();

    const payload: JwtPayload = {
      email: email,
      sub: userId,
      workspaceId: workspace._id.toString(),
    };

    const token = this.jwtService.sign(payload);
    return {
      message: "Email verification successful",
      token,
    };
  }

  async setWebsite(
    setWebsiteDto: SetWebsiteDto,
    userId: string,
    workspaceId: string
  ): Promise<{ message: string; websiteID: string }> {
    try {
      // Check registration process
      const registration = await this.registrationModel
        .findOne({ owner: userId, workspace: workspaceId })
        .exec();
      if (!registration) {
        throw I18nHelper.notFound("errors.auth.not_found");
      }

      const workspace = await this.workspaceService.updateDefaultWorkspace(
        workspaceId,
        userId,
        {
          name: setWebsiteDto.workspaceName,
          websiteUrl: setWebsiteDto.websiteUrl,
        }
      );

      //for onlychat, update name for user if it's not existing when registering
      const user = await this.userService.findOne(userId);
      if (user && !user.firstName) {
        await this.userService.updateOne(userId, {
          firstName: setWebsiteDto.workspaceName,
        });
      }

      registration.registrationStep = 3;
      await registration.save();

      return {
        message: "Website information updated successfully",
        websiteID: workspace.websiteID,
      };
    } catch (error) {
      throw I18nHelper.badRequest("errors.auth.update_failed");
    }
  }

  async completeRegistration(
    completeDto: EndRegisterDto,
    userId: string,
    workspaceId: string
  ): Promise<{ message: string }> {
    // Find registration information
    const registration = await this.registrationModel
      .findOne({
        owner: userId,
        workspace: workspaceId,
      })
      .exec();

    if (!registration) {
      throw I18nHelper.notFound("errors.auth.not_found");
    }

    // Update additional information
    registration.companySize = completeDto.companySize;
    registration.messagingPlatform = completeDto.messagingPlatform || [];
    registration.registrationStep = 4; // Complete registration
    await registration.save();

    const user = await this.userService.findOne(userId);
    if (!user) {
      throw I18nHelper.notFound("errors.users.not_found");
    }

    const firstName = user.firstName || "";

    // Send welcome email to the new user
    try {
      await this.mailService.sendWelcomeEmail(registration.email, firstName);
    } catch (error) {
      console.error(
        `Failed to send welcome email to ${registration.email}:`,
        error
      );
    }

    return {
      message: "Registration information updated successfully",
    };
  }

  async inviteTeam(
    inviteTeamDto: InviteTeamDto,
    userId: string,
    workspaceId: string
  ): Promise<{ message: string }> {
    const { emails } = inviteTeamDto;

    for (const email of emails) {
      await this.workspaceMemberService.addMember(
        workspaceId,
        { email, role: WorkspaceMemberRole.DEVELOPER },
        userId
      );
    }

    return {
      message: "Team members invited successfully",
    };
  }

  private async doLogin(user: UserDocument) {
    const userWorkspaces = await this.workspaceService.getAccessibleWorkspaces(
      user._id.toString()
    );

    if (userWorkspaces.length == 0) {
      const workspace = await this.workspaceService.createDefaultWorkspace(
        user._id.toString(),
        user.firstName
      );
      userWorkspaces.push(workspace);
    }

    // automatically login with the first workspace
    const workspace = userWorkspaces[0];

    const payload: JwtPayload = {
      email: user.email,
      sub: user._id.toString(),
      workspaceId: workspace._id.toString(),
    };
    const token = this.jwtService.sign(payload);

    return {
      message: "Login successful",
      token,
      userId: user._id.toString(),
      workspaceId: workspace._id.toString(),
      workspaces: userWorkspaces,
    };
  }

  async login(loginDto: LoginDto): Promise<{
    message: string;
    token: string;
    userId: string;
    workspaceId: string;
    workspaces: any[];
  }> {
    const { email, password } = loginDto;

    // Find user by email
    const user = await this.userService.findByEmail(email);
    if (!user || user.status == UserStatus.PENDING) {
      throw I18nHelper.unauthorized("errors.auth.email_not_registered");
    }

    if (!user.password && user.googleId) {
      throw I18nHelper.unauthorized("errors.auth.registered_with_google");
    }

    if (user.status === UserStatus.BLOCKED) {
      throw I18nHelper.unauthorized("errors.auth.user_is_blocked");
    }

    // Check if user is verified
    if (user.status !== UserStatus.ACTIVE) {
      throw I18nHelper.unauthorized("errors.auth.user_not_verified");
    }

    // Verify password
    const isPasswordValid = await bcrypt.compare(password, user.password ?? "");
    if (!isPasswordValid) {
      throw I18nHelper.unauthorized("errors.auth.invalid_password");
    }

    return this.doLogin(user);
  }

  async generateResetToken(
    email: string,
    analyticsData?: AnalyticsData
  ): Promise<void> {
    const user = await this.userService.findByEmail(email);
    if (!user) {
      throw I18nHelper.notFound("errors.auth.email_not_registered");
    }

    const resetToken = crypto.randomBytes(32).toString("hex");
    const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour

    await this.passwordResetTokenModel.create({
      email: email,
      token: resetToken,
      expiresAt: resetTokenExpires,
      isUsed: false,
    });

    await this.mailService.sendResetPassword(
      email,
      user.firstName || "",
      resetToken,
      analyticsData
    );
  }

  async resetPassword(token: string, newPassword: string): Promise<void> {
    const passwordResetToken = await this.passwordResetTokenModel.findOne({
      token: token,
      expiresAt: { $gt: new Date() },
    });
    if (!passwordResetToken) {
      throw I18nHelper.badRequest("Invalid or expired reset token");
    }
    if (passwordResetToken.isUsed) {
      throw I18nHelper.badRequest("Invalid reset token");
    }

    const user = await this.userService.findByEmail(passwordResetToken.email);
    if (!user) {
      throw I18nHelper.notFound("errors.auth.email_not_registered");
    }
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await this.userService.updatePassword(user._id.toString(), hashedPassword);
    await this.passwordResetTokenModel.findByIdAndUpdate(user._id, {
      isUsed: true,
    });
  }

  async loginWithWorkspace(
    userId: string,
    workspaceId: string
  ): Promise<{ message: string; token: string; userId: string }> {
    // Find user by email
    const user = await this.userService.findOne(userId);
    if (!user) {
      throw I18nHelper.unauthorized("errors.auth.email_not_registered");
    }

    // Check if user is verified
    if (user.status !== UserStatus.ACTIVE) {
      throw I18nHelper.unauthorized("errors.auth.user_not_verified");
    }

    const isMember = await this.workspaceMemberService.isMember(
      workspaceId,
      userId
    );

    if (isMember) {
      // Generate JWT token
      const payload: JwtPayload = {
        email: user.email,
        sub: user._id.toString(),
        workspaceId: workspaceId,
      };

      const token = this.jwtService.sign(payload);

      return {
        message: "Login with workspace successful",
        token,
        userId: user._id.toString(),
      };
    } else {
      throw I18nHelper.unauthorized("errors.workspaces.unauthorized");
    }
  }

  async loginWithGoogle(googleUser: GoogleUser): Promise<{
    message: string;
    token: string;
    userId: string;
    workspaceId: string;
    workspaces: any[];
  }> {
    const { userId, email, firstName, lastName, picture } = googleUser;

    // Find user by email
    let user = await this.userService.findByEmail(email);
    if (!user) {
      user = await this.userService.createGoogleUser(
        firstName || "",
        lastName || "",
        email,
        userId,
        picture
      );
      await this.mailService.sendWelcomeEmail(email, firstName || "");
    } else {
      if (user.googleId && user.googleId !== userId) {
        throw I18nHelper.unauthorized("errors.auth.registered_with_google");
      }
      if (!user.googleId) {
        await this.userService.updateOne(user._id.toString(), {
          googleId: userId,
        });
      }
    }

    if (user.status === UserStatus.BLOCKED) {
      throw I18nHelper.unauthorized("errors.auth.user_is_blocked");
    }

    if (user.status == UserStatus.PENDING) {
      await this.userService.updateOne(user._id.toString(), {
        status: UserStatus.ACTIVE,
      });
    }

    return this.doLogin(user);
  }
}
