import {
  Body,
  Controller,
  Get,
  HttpCode,
  HttpStatus,
  Inject,
  Post,
  UseGuards,
  Request,
  Req,
  Res,
  Param,
} from "@nestjs/common";
import {
  Response as ExpressResponse,
  Request as ExpressRequest,
} from "express";
import { AuthServiceInterface } from "../interfaces/auth-service.interface";
import { RegisterDto } from "../dto/register.dto";
import { VerifyCodeDto } from "../dto/verify-code.dto";
import { EndRegisterDto } from "../dto/end-register.dto";
import { ResendCodeDto } from "../dto/resend-code.dto";
import { JwtAuthGuard } from "../guards/jwt-auth.guard";
import {
  ApiBearerAuth,
  ApiExcludeEndpoint,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { LoginDto } from "../dto/login.dto";
import { ResetPasswordDto } from "../dto/reset-password.dto";
import { ForgotPasswordDto } from "../dto/forgot-password.dto";
import { I18nHelper } from "src/common/utils/i18n.helper";
import { RequestWithUser } from "../interfaces/jwt-payload.interface";
import { SetWebsiteDto } from "../dto/set-website.dto";
import { InviteTeamDto } from "../dto/invite-team.dto";
import { AuthGuard } from "@nestjs/passport";
import { RequestWithGoogleUser } from "../interfaces/google-payload.interface";
import { ConfigService } from "@nestjs/config";
import { GoogleUserRedisService } from "src/services/redis/google-user.redis.service";
import { AnalyticsService } from "src/common/services/analytics.service";

@ApiTags("Authentication")
@Controller("auth")
export class AuthController {
  constructor(
    @Inject("AuthServiceInterface")
    private readonly authService: AuthServiceInterface,
    private readonly configService: ConfigService,
    private readonly googleUserRedisService: GoogleUserRedisService,
    private readonly analyticsService: AnalyticsService
  ) {}

  @Post("register")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Register: Step 1" })
  @ApiResponse({
    status: 200,
    description: "Registration process started successfully",
  })
  @ApiResponse({ status: 409, description: "Email already in use" })
  async register(
    @Body() registerDto: RegisterDto,
    @Request() req: ExpressRequest
  ) {
    const analyticsData = this.analyticsService.extractAnalyticsData(req);
    return this.authService.startRegistration(registerDto, analyticsData);
  }

  @Post("verify-code")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Register: Step 2" })
  @ApiResponse({ status: 200, description: "Email verification successful" })
  @ApiResponse({
    status: 400,
    description: "Invalid or expired verification code",
  })
  @ApiResponse({
    status: 404,
    description: "Registration information not found",
  })
  async verifyCode(@Body() verifyDto: VerifyCodeDto) {
    return this.authService.verifyCode(verifyDto);
  }

  @Post("set-website")
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Register: Step 3" })
  @ApiResponse({
    status: 200,
    description: "Set website successfully",
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async setWebsite(
    @Body() setWebsiteDto: SetWebsiteDto,
    @Request() req: RequestWithUser
  ): Promise<{ message: string; websiteID: string }> {
    return this.authService.setWebsite(
      setWebsiteDto,
      req.user.sub,
      req.user.workspaceId
    );
  }

  @Post("complete-register")
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Register: Step 4" })
  @ApiResponse({
    status: 200,
    description: "Registration completed successfully",
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async endRegister(
    @Body() completeDto: EndRegisterDto,
    @Request() req: RequestWithUser
  ) {
    return this.authService.completeRegistration(
      completeDto,
      req.user.sub,
      req.user.workspaceId
    );
  }

  @Post("invite-team")
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Invite team members" })
  @ApiResponse({
    status: 200,
    description: "Team members invited successfully",
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async inviteTeam(
    @Body() inviteTeamDto: InviteTeamDto,
    @Request() req: RequestWithUser
  ) {
    return this.authService.inviteTeam(
      inviteTeamDto,
      req.user.sub,
      req.user.workspaceId
    );
  }

  @Post("resend-code")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Resend verification code" })
  @ApiResponse({ status: 200, description: "Verification code has been sent" })
  async resendVerificationCode(
    @Body() resendCodeDto: ResendCodeDto,
    @Request() req: ExpressRequest
  ) {
    const analyticsData = this.analyticsService.extractAnalyticsData(req);
    return this.authService.sendVerificationCode(
      resendCodeDto.email,
      analyticsData
    );
  }

  @Post("login")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Login with email and password" })
  @ApiResponse({ status: 200, description: "Login successful" })
  @ApiResponse({
    status: 401,
    description: "Invalid credentials or user not verified",
  })
  async login(@Body() loginDto: LoginDto): Promise<{
    message: string;
    token: string;
    userId: string;
    workspaceId: string;
    workspaces: any[];
  }> {
    return this.authService.login(loginDto);
  }

  @Post("password-reset/request")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Request a password reset email" }) // Add this line
  @ApiResponse({ status: 200, description: "Request reset password" })
  async requestPasswordReset(
    @Body() body: ForgotPasswordDto,
    @Request() req: ExpressRequest
  ) {
    const analyticsData = this.analyticsService.extractAnalyticsData(req);
    await this.authService.generateResetToken(body.email, analyticsData);
    return { message: "Password reset email sent" };
  }

  @Post("password-reset")
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: "Reset password with token" }) // Add this line
  @ApiResponse({ status: 200, description: "Reset password successful" })
  async resetPassword(@Body() body: ResetPasswordDto) {
    if (body.password !== body.confirmPassword) {
      throw I18nHelper.badRequest("Passwords do not match");
    }

    await this.authService.resetPassword(body.token, body.password);
    return { message: "Password reset successfully" };
  }

  @Get("google")
  @UseGuards(AuthGuard("google"))
  async googleAuth(): Promise<void> {}

  @Get("google/redirect")
  @UseGuards(AuthGuard("google"))
  @ApiExcludeEndpoint()
  async googleAuthRedirect(
    @Req() req: RequestWithGoogleUser,
    @Res() res: ExpressResponse
  ): Promise<void> {
    try {
      const token = await this.googleUserRedisService.storeGoogleUser(req.user);
      const redirectUrl = `${this.configService.get<string>(
        "FRONTEND_URL"
      )}/auth/login?token=${token}`;
      res.redirect(redirectUrl);
    } catch (error) {
      throw new Error(error instanceof Error ? error.message : String(error));
    }
  }

  @Get("google/login/:token")
  @ApiOperation({ summary: "Login with Google" })
  @ApiResponse({ status: 200, description: "Login successful" })
  @ApiResponse({
    status: 401,
    description: "Invalid credentials or user not verified",
  })
  async loginWithGoogle(
    @Param("token") token: string,
    @Res() res: ExpressResponse
  ) {
    const googleUser = await this.googleUserRedisService.getGoogleUser(token);
    if (!googleUser) {
      throw I18nHelper.unauthorized("errors.auth.invalid_token");
    }

    const results = await this.authService.loginWithGoogle(googleUser);
    res.send(results);
  }
}
