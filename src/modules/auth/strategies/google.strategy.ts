import { PassportStrategy } from "@nestjs/passport";
import { Injectable } from "@nestjs/common";
import { Strategy, VerifyCallback, Profile } from "passport-google-oauth20";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, "google") {
  constructor(private configService: ConfigService) {
    const googleClientId = configService.get<string>("GOOGLE_CLIENT_ID");

    if (!googleClientId) {
      throw new Error("GOOGLE_CLIENT_ID is not defined in the environment");
    }

    const googleClientSecret = configService.get<string>(
      "GOOGLE_CLIENT_SECRET"
    );

    if (!googleClientSecret) {
      throw new Error("GOOGLE_CLIENT_SECRET is not defined in the environment");
    }

    const googleCallbackUrl = configService.get<string>("GOOGLE_CALLBACK_URL");

    if (!googleCallbackUrl) {
      throw new Error("GOOGLE_CALLBACK_URL is not defined in the environment");
    }

    super({
      clientID: googleClientId,
      clientSecret: googleClientSecret,
      callbackURL: googleCallbackUrl,
      scope: ["email", "profile"],
    });
  }

  validate(
    accessToken: string,
    refreshToken: string,
    profile: Profile,
    done: VerifyCallback
  ): any {
    const { name, emails, photos } = profile;
    const user = {
      userId: profile.id,
      email: emails?.[0]?.value || "",
      firstName: name?.givenName || "",
      lastName: name?.familyName || "",
      picture: photos?.[0]?.value || "",
    };
    done(null, user);
  }
}
