import { User } from "@app/users/schemas/user.schema";
import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument, Schema as MongooseSchema } from "mongoose";
import { Workspace } from "src/modules/workspace/schemas/workspace.schema";

export type RegistrationDocument = HydratedDocument<Registration>;

@Schema({ timestamps: true })
export class Registration {
  @Prop()
  name: string;

  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: true })
  password: string; // Note: will be hashed before storing

  @Prop({ required: true })
  phoneNumber: string;

  @Prop({ default: false })
  isEmailVerified: boolean;

  @Prop()
  workspaceName: string;

  @Prop()
  websiteUrl: string;

  @Prop()
  companySize: string;

  @Prop({ type: [String], default: [] })
  messagingPlatform: string[];

  @Prop({ default: 1 })
  registrationStep: number;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User", required: false })
  owner: User;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: false,
  })
  workspace: Workspace;

  @Prop()
  expiresAt: Date;
}

export const RegistrationSchema = SchemaFactory.createForClass(Registration);
