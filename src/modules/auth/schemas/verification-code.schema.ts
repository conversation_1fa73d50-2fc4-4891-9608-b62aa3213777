import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { HydratedDocument } from "mongoose";

export type VerificationCodeDocument = HydratedDocument<VerificationCode>;

@Schema({ timestamps: true })
export class VerificationCode {
  @Prop({ required: true })
  email: string;

  @Prop({ required: true })
  code: string;

  @Prop({ required: true })
  expiresAt: Date;

  @Prop({ default: false })
  used: boolean;
}

export const VerificationCodeSchema =
  SchemaFactory.createForClass(VerificationCode);
