import {
  <PERSON><PERSON><PERSON>y,
  <PERSON><PERSON>ot<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  IsUrl,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class SetWebsiteDto {
  @ApiProperty({
    description: "Workspace name",
    example: "Acme Corporation",
  })
  @IsString({ message: "Name must be a string" })
  @IsNotEmpty({ message: "Name is required" })
  workspaceName: string;

  @ApiProperty({
    description: "Website URL",
    example: "https://acme.com",
    required: true,
  })
  @IsUrl({}, { message: "Invalid website URL format" })
  websiteUrl: string;

  @ApiProperty({
    description: "Company size range",
    example: "10-50",
    required: false,
  })
  @IsString({ message: "Company size must be a string" })
  @IsOptional()
  companySize: string;

  @ApiProperty({
    description: "Preferred messaging platforms",
    example: ["Slack", "Microsoft Teams", "Discord"],
    type: [String],
    required: false,
  })
  @IsArray({ message: "Messaging platforms must be an array" })
  @IsString({ each: true, message: "Each messaging platform must be a string" })
  @IsOptional()
  messagingPlatform: string[];
}
