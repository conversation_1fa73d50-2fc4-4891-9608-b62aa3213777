import { ApiProperty } from "@nestjs/swagger";
import { IsString, <PERSON><PERSON>ength, <PERSON>, IsNotEmpty } from "class-validator";

export class ResetPasswordDto {
  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty()
  @IsString({ message: "errors.validation.password_must_be_string" })
  @MinLength(8, { message: "Password must be at least 8 characters" })
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message:
      "Password must include uppercase, lowercase, and a number or special character",
  })
  @IsNotEmpty({ message: "errors.validation.password_required" })
  password: string;

  @ApiProperty()
  @IsString()
  @IsNotEmpty()
  confirmPassword: string;
}
