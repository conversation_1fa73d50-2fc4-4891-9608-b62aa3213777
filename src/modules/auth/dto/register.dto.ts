import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>E<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  IsOptional,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class RegisterDto {
  @ApiProperty({
    description: "User name",
    example: "<PERSON>",
  })
  @IsString({ message: "Name must be a string" })
  @IsOptional()
  @IsNotEmpty({ message: "Name is required" })
  name: string;

  @ApiProperty({
    description: "User email address",
    example: "<EMAIL>",
  })
  @IsEmail({}, { message: "errors.validation.invalid_email" })
  @IsNotEmpty({ message: "errors.validation.email_required" })
  email: string;

  @ApiProperty({
    description:
      "User password - must include uppercase, lowercase, and a number or special character",
    example: "Password123!",
    minLength: 8,
  })
  @IsString({ message: "errors.validation.password_must_be_string" })
  @MinLength(8, { message: "Password must be at least 8 characters" })
  @Matches(/((?=.*\d)|(?=.*\W+))(?![.\n])(?=.*[A-Z])(?=.*[a-z]).*$/, {
    message:
      "Password must include uppercase, lowercase, and a number or special character",
  })
  @IsNotEmpty({ message: "errors.validation.password_required" })
  password: string;

  @ApiProperty({
    description: "User phone number",
    example: "1234567890",
  })
  @IsString({ message: "Phone number must be a string" })
  @IsNotEmpty({ message: "Phone number is required" })
  phoneNumber: string;
}
