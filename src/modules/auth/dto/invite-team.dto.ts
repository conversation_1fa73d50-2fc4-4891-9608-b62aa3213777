import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsOptional } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class InviteTeamDto {
  @ApiProperty({
    description: "List of developers to invite",
    example: ["<EMAIL>", "<EMAIL>"],
    type: [String],
    required: false,
  })
  @IsArray({ message: "Invited developers must be an array" })
  @IsEmail({}, { each: true, message: "Each email must be valid" })
  @IsOptional()
  emails: string[];
}
