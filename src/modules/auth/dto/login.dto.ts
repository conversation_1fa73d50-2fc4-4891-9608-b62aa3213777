import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>E<PERSON><PERSON>, <PERSON>S<PERSON> } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class LoginDto {
  @ApiProperty({
    description: "User email address",
    example: "<EMAIL>",
  })
  @IsEmail({}, { message: "errors.validation.invalid_email" })
  @IsNotEmpty({ message: "errors.validation.email_required" })
  email: string;

  @ApiProperty({
    description: "User password",
    example: "Password123!",
  })
  @IsString({ message: "errors.validation.password_must_be_string" })
  @IsNotEmpty({ message: "errors.validation.password_required" })
  password: string;
}
