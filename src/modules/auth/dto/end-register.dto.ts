import { Is<PERSON><PERSON><PERSON>, <PERSON>Optional, IsString } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class EndRegisterDto {
  @ApiProperty({
    description: "Company size range",
    example: "10-50",
    required: false,
  })
  @IsString({ message: "Company size must be a string" })
  @IsOptional()
  companySize: string;

  @ApiProperty({
    description: "Preferred messaging platforms",
    example: ["Slack", "Microsoft Teams", "Discord"],
    type: [String],
    required: false,
  })
  @IsArray({ message: "Messaging platforms must be an array" })
  @IsString({ each: true, message: "Each messaging platform must be a string" })
  @IsOptional()
  messagingPlatform: string[];
}
