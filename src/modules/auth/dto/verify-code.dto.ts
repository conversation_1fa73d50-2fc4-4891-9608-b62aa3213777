import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Empty, <PERSON>S<PERSON>, Length } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class VerifyCodeDto {
  @ApiProperty({
    description: "User email address for verification",
    example: "<EMAIL>",
  })
  @IsEmail({}, { message: "errors.validation.invalid_email" })
  @IsNotEmpty({ message: "errors.validation.email_required" })
  email: string;

  @ApiProperty({
    description: "Six-character verification code sent to the user",
    example: "123456",
    minLength: 6,
    maxLength: 6,
  })
  @IsString({ message: "Verification code must be a string" })
  @IsNotEmpty({ message: "Verification code is required" })
  @Length(6, 6, { message: "Verification code must be 6 characters" })
  code: string;
}
