import { RegisterDto } from "../dto/register.dto";
import { EndRegisterDto } from "../dto/end-register.dto";
import { VerifyCodeDto } from "../dto/verify-code.dto";
import { LoginDto } from "../dto/login.dto";
import { SetWebsiteDto } from "../dto/set-website.dto";
import { InviteTeamDto } from "../dto/invite-team.dto";
import { GoogleUser } from "./google-payload.interface";
import { AnalyticsData } from "src/common/services/analytics.service";

export interface AuthServiceInterface {
  /**
   * Start registration process - Initial registration
   * @param registerDto Initial registration information (email, password, phoneNumber)
   * @param analyticsData Optional analytics data (IP, country, device info)
   */
  startRegistration(
    registerDto: RegisterDto,
    analyticsData?: AnalyticsData
  ): Promise<{ message: string; registrationId: string }>;

  /**
   * Send verification code to email
   * @param email Email to send verification code to
   * @param analyticsData Optional analytics data (IP, country, device info)
   */
  sendVerificationCode(
    email: string,
    analyticsData?: AnalyticsData
  ): Promise<{ message: string }>;

  /**
   * Verify code - Verification step
   * @param verifyDto Verification information including email and code
   */
  verifyCode(
    verifyDto: VerifyCodeDto
  ): Promise<{ message: string; token: string }>;

  /**
   * Set website information - Second registration step
   * @param setWebsiteDto Website information
   * @param userId User's ID
   * @param workspaceId Workspace's ID
   */
  setWebsite(
    setWebsiteDto: SetWebsiteDto,
    userId: string,
    workspaceId: string
  ): Promise<{ message: string; websiteID: string }>;

  /**
   * Complete registration process - Final registration step
   * @param completeDto Additional registration information
   * @param userId User's ID
   * @param workspaceId Workspace's ID
   */
  completeRegistration(
    completeDto: EndRegisterDto,
    userId: string,
    workspaceId: string
  ): Promise<{ message: string }>;

  inviteTeam(
    inviteTeamDto: InviteTeamDto,
    userId: string,
    workspaceId: string
  ): Promise<{ message: string }>;

  /**
   * Login with email and password
   * @param loginDto User credentials
   */
  login(loginDto: LoginDto): Promise<{
    message: string;
    token: string;
    userId: string;
    workspaceId: string;
    workspaces: any[];
  }>;

  generateResetToken(
    email: string,
    analyticsData?: AnalyticsData
  ): Promise<void>;

  resetPassword(token: string, newPassword: string): Promise<void>;

  loginWithWorkspace(
    userId: string,
    workspaceId: string
  ): Promise<{ message: string; token: string; userId: string }>;

  loginWithGoogle(googleUser: GoogleUser): Promise<{
    message: string;
    token: string;
    userId: string;
    workspaceId: string;
    workspaces: any[];
  }>;
}
