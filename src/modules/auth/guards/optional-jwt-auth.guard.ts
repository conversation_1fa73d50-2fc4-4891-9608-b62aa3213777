/* eslint-disable @typescript-eslint/no-unsafe-return */
import { ExecutionContext, Injectable, Logger } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";

@Injectable()
export class OptionalJwtAuthGuard extends AuthGuard("jwt") {
  private readonly logger = new Logger(OptionalJwtAuthGuard.name);

  constructor() {
    super();
    this.logger.log("OptionalJwtAuthGuard initialized");
  }

  handleRequest(err: any, user: any, info: any, context: ExecutionContext) {
    // Instead of throwing an error, return the user if exists or null
    return user || null;
  }
}
