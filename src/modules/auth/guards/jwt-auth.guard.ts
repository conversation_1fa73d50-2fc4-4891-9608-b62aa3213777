import { Injectable, UnauthorizedException, Logger } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";

@Injectable()
export class JwtAuthGuard extends AuthGuard("jwt") {
  private readonly logger = new Logger(JwtAuthGuard.name);

  constructor() {
    super();
    this.logger.log("JwtAuthGuard initialized");
  }

  // Override handleRequest to provide custom error messages
  handleRequest<TUser = any>(err: any, user: any, info: any): TUser {
    if (err || !user) {
      this.logger.error("Authentication error:", err);
      if (info) {
        this.logger.debug("Token info:", info);
      }
      throw new UnauthorizedException("Invalid or expired token");
    }
    return user as unknown as TUser;
  }
}
