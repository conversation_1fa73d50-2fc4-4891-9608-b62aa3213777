import { Injectable, UnauthorizedException, Logger } from "@nestjs/common";
import { AuthGuard } from "@nestjs/passport";
import { JwtPayload } from "../interfaces/jwt-payload.interface";

@Injectable()
export class JwtAuthWorkspaceGuard extends AuthGuard("jwt") {
  private readonly logger = new Logger(JwtAuthWorkspaceGuard.name);

  constructor() {
    super();
    this.logger.log("JwtAuthWorkspaceGuard initialized");
  }

  handleRequest<TUser = any>(err: any, user: JwtPayload, info: any): TUser {
    if (err || !user) {
      this.logger.error("Authentication error:", err);
      if (info) {
        this.logger.debug("Token info:", info);
      }
      throw new UnauthorizedException("Invalid or expired token");
    }
    if (user.workspaceId == null) {
      throw new UnauthorizedException("You need to set current workspace");
    }

    return user as unknown as TU<PERSON>;
  }
}
