import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from "@nestjs/common";
import { JwtAuthGuard } from "./jwt-auth.guard";
import { Reflector } from "@nestjs/core";

interface UserWithRole {
  email?: string;
  role?: string;
}

@Injectable()
export class AdminGuard implements CanActivate {
  private readonly logger = new Logger(AdminGuard.name);
  private jwtAuthGuard: JwtAuthGuard;

  constructor(private reflector: Reflector) {
    this.jwtAuthGuard = new JwtAuthGuard();
    this.logger.log("AdminGuard initialized");
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // First verify the JWT token using the JWT auth guard
    const isAuthenticated = await this.jwtAuthGuard.canActivate(context);

    if (!isAuthenticated) {
      return false;
    }

    // Get the request object
    const request = context.switchToHttp().getRequest();
    const user = request.user as UserWithRole;

    // Check if user is an admin
    if (!user || user.role !== "admin") {
      this.logger.warn(
        `Access denied for non-admin user: ${user?.email || "unknown"}`
      );
      throw new UnauthorizedException("Admin access required");
    }

    return true;
  }
}
