/* eslint-disable @typescript-eslint/no-unsafe-member-access */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { createParamDecorator, ExecutionContext } from "@nestjs/common";
import { JwtPayload } from "../interfaces/jwt-payload.interface";

export const CurrentUser = createParamDecorator(
  (_data, context: ExecutionContext): JwtPayload => {
    const ctx = context.getArgByIndex(2); // GraphQL context
    return ctx.req.user as JwtPayload;
  }
);
