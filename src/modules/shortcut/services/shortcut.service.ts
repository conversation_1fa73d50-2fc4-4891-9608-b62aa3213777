import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { ShortcutServiceInterface } from "../interfaces/shortcut-service.interface";
import { Shortcut, ShortcutDocument } from "../entities/shortcut.entity";
import { CreateShortcutDto } from "../dto/create-shortcut.dto";
import { UpdateShortcutDto } from "../dto/update-shortcut.dto";
import { ListShortcutsDto } from "../dto/list-shortcuts.dto";

@Injectable()
export class ShortcutService implements ShortcutServiceInterface {
  constructor(
    @InjectModel("Shortcut")
    private shortcutModel: Model<Shortcut>
  ) {}

  async createShortcut(
    workspaceId: string,
    createDto: CreateShortcutDto
  ): Promise<ShortcutDocument> {
    const shortcut = new this.shortcutModel({
      ...createDto,
      workspace: new Types.ObjectId(workspaceId),
    });
    return shortcut.save();
  }

  async updateShortcut(
    id: string,
    updateDto: UpdateShortcutDto
  ): Promise<ShortcutDocument> {
    const updatedShortcut = await this.shortcutModel
      .findByIdAndUpdate(id, updateDto, { new: true })
      .exec();

    if (!updatedShortcut) {
      throw new Error("Shortcut not found");
    }

    return updatedShortcut;
  }

  async removeShortcut(id: string, userId: string): Promise<boolean> {
    const result = await this.shortcutModel.findByIdAndDelete(id).exec();
    return !!result;
  }

  async getShortcuts(
    workspaceId: string,
    inputDto: ListShortcutsDto
  ): Promise<ShortcutDocument[]> {
    const { page, limit, keyword } = inputDto;
    const skip = (page - 1) * limit;

    const query: Record<string, any> = {
      workspace: new Types.ObjectId(workspaceId),
    };

    if (keyword) {
      query["$or"] = [
        { shortcut: { $regex: keyword, $options: "i" } },
        { message: { $regex: keyword, $options: "i" } },
        { tag: { $regex: keyword, $options: "i" } },
      ];
    }

    return this.shortcutModel.find(query).skip(skip).limit(limit).exec();
  }

  async countShortcuts(
    workspaceId: string,
    inputDto: ListShortcutsDto,
    ignorePagination?: boolean
  ): Promise<number> {
    const { keyword } = inputDto;

    const query: Record<string, any> = {
      workspace: new Types.ObjectId(workspaceId),
    };

    if (keyword) {
      query["$or"] = [
        { shortcut: { $regex: keyword, $options: "i" } },
        { message: { $regex: keyword, $options: "i" } },
        { tag: { $regex: keyword, $options: "i" } },
      ];
    }

    return this.shortcutModel.countDocuments(query).exec();
  }
}
