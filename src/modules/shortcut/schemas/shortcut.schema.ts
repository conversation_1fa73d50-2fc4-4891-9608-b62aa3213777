import { <PERSON><PERSON>, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Schema as MongooseSchema } from "mongoose";
import { Workspace } from "src/modules/workspace/schemas/workspace.schema";

@Schema({ timestamps: true })
export class Shortcut {
  @Prop({ required: true })
  shortcut: string;

  @Prop({ required: true })
  message: string;

  @Prop()
  tag?: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: true,
  })
  workspace: Workspace;
}

export const ShortcutSchema = SchemaFactory.createForClass(Shortcut);
