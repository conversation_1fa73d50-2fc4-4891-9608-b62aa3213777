import {
  Controller,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Inject,
  Request,
  Put,
  Query,
  Get,
} from "@nestjs/common";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { CreateShortcutDto } from "../dto/create-shortcut.dto";
import { UpdateShortcutDto } from "../dto/update-shortcut.dto";
import { ShortcutServiceInterface } from "../interfaces/shortcut-service.interface";
import { ListShortcutsDto } from "../dto/list-shortcuts.dto";
import { JwtAuthWorkspaceGuard } from "src/modules/auth/guards/jwt-auth-workspace.guard";
import { Shortcut } from "../entities/shortcut.entity";
import { PaginationResponseDto } from "src/modules/common/dto/pagination.dto";

@ApiTags("Shortcuts")
@Controller("shortcuts")
@UseGuards(JwtAuthWorkspaceGuard)
@ApiBearerAuth()
export class ShortcutController {
  constructor(
    @Inject("ShortcutServiceInterface")
    private readonly shortcutService: ShortcutServiceInterface
  ) {}

  @Post()
  @ApiOperation({ summary: "Create a new shortcut in the workspace" })
  @ApiResponse({ status: 201, description: "Shortcut created successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  async create(
    @Body() createDto: CreateShortcutDto,
    @Request() req: RequestWithUser
  ): Promise<Shortcut> {
    try {
      return new Shortcut(
        await this.shortcutService.createShortcut(
          req.user.workspaceId!,
          createDto
        )
      );
    } catch (error) {
      throw new Error(error);
    }
  }

  @Put(":id")
  @ApiOperation({ summary: "Update a shortcut" })
  @ApiParam({ name: "id", description: "Shortcut ID" })
  @ApiResponse({ status: 200, description: "Shortcut updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Shortcut not found" })
  async update(
    @Param("id") id: string,
    @Body() updateDto: UpdateShortcutDto,
    @Request() req: RequestWithUser
  ): Promise<Shortcut> {
    try {
      return new Shortcut(
        await this.shortcutService.updateShortcut(id, updateDto)
      );
    } catch (error) {
      throw new Error(error);
    }
  }

  @Delete(":id")
  @ApiOperation({ summary: "Delete a shortcut" })
  @ApiParam({ name: "id", description: "Shortcut ID" })
  @ApiResponse({ status: 200, description: "Shortcut deleted successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Shortcut not found" })
  remove(
    @Param("id") id: string,
    @Request() req: RequestWithUser
  ): Promise<boolean> {
    return this.shortcutService.removeShortcut(id, req.user.sub);
  }

  @Get()
  @ApiOperation({ summary: "Get all shortcuts in the workspace" })
  @ApiResponse({ status: 200, description: "List of shortcuts" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  async getShortcuts(
    @Query() inputDto: ListShortcutsDto,
    @Request() req: RequestWithUser
  ): Promise<PaginationResponseDto<Shortcut>> {
    try {
      const [shortcuts, total] = await Promise.all([
        this.shortcutService.getShortcuts(req.user.workspaceId!, inputDto),
        this.shortcutService.countShortcuts(req.user.workspaceId!, inputDto),
      ]);
      return {
        data: shortcuts.map((item) => new Shortcut(item)),
        total,
        page: inputDto.page,
        hasNextPage: total - inputDto.page * inputDto.limit > 0,
      };
    } catch (error) {
      throw new Error(error);
    }
  }
}
