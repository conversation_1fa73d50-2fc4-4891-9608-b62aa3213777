import { Types } from "mongoose";
import { WorkspaceDocument } from "src/modules/workspace/entities/workspace.entity";
import {
  BaseDocument,
  BaseEntity,
} from "src/modules/common/entities/base.entity";

abstract class BaseShortcut extends BaseEntity {
  shortcut!: string;
  message!: string;
  tag?: string;
  workspace?: WorkspaceDocument;
  workspaceId?: string;
}

export interface ShortcutDocument extends BaseDocument<BaseShortcut> {
  _id: Types.ObjectId;
}

export class Shortcut extends BaseShortcut {
  constructor(doc?: ShortcutDocument) {
    super();
    if (doc) {
      super.assignFields(doc);

      this.shortcut = doc.shortcut;
      this.message = doc.message;
      this.tag = doc.tag;
      this.workspaceId = doc.workspace?._id.toString();
    }
  }
}
