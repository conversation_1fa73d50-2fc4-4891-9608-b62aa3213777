import { CreateShortcutDto } from "../dto/create-shortcut.dto";
import { UpdateShortcutDto } from "../dto/update-shortcut.dto";
import { PaginationResponseDto } from "src/modules/common/dto/pagination.dto";
import { ListShortcutsDto } from "../dto/list-shortcuts.dto";
import { ShortcutDocument } from "../entities/shortcut.entity";

export interface ShortcutServiceInterface {
  createShortcut(
    workspaceId: string,
    createDto: CreateShortcutDto
  ): Promise<ShortcutDocument>;

  updateShortcut(
    id: string,
    updateDto: UpdateShortcutDto
  ): Promise<ShortcutDocument>;

  removeShortcut(id: string, userId: string): Promise<boolean>;

  getShortcuts(
    workspaceId: string,
    inputDto: ListShortcutsDto
  ): Promise<ShortcutDocument[]>;

  countShortcuts(
    workspaceId: string,
    inputDto: ListShortcutsDto,
    ignorePagination?: boolean
  ): Promise<number>;
}
