import { forwardRef, Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { WorkspaceModule } from "../workspace/workspace.module";
import { Shortcut, ShortcutSchema } from "./schemas/shortcut.schema";
import { ShortcutController } from "./controllers/shortcut.controller";
import { ShortcutService } from "./services/shortcut.service";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Shortcut.name, schema: ShortcutSchema },
    ]),
    forwardRef(() => WorkspaceModule),
  ],
  controllers: [ShortcutController],
  providers: [
    {
      provide: "ShortcutServiceInterface",
      useClass: ShortcutService,
    },
  ],
  exports: ["ShortcutServiceInterface"],
})
export class ShortcutModule {}
