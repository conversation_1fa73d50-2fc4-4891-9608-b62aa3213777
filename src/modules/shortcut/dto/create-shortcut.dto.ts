import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsNotEmpty } from "class-validator";

export class CreateShortcutDto {
  @ApiProperty({
    description: "Shortcut",
    required: true,
    example: "shortcut",
  })
  @IsString()
  shortcut!: string;

  @ApiProperty({
    description: "Message",
    required: true,
    example: "message",
  })
  @IsString()
  message!: string;

  @ApiProperty({
    description: "Tag",
    required: false,
    example: "tag",
  })
  @IsString()
  tag?: string;
}
