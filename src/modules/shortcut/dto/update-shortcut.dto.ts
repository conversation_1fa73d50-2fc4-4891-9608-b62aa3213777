import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional } from "class-validator";

export class UpdateShortcutDto {
  @ApiProperty({
    description: "Shortcut",
    required: false,
    example: "shortcut",
  })
  @IsString()
  @IsOptional()
  shortcut?: string;

  @ApiProperty({
    description: "Message",
    required: false,
    example: "message",
  })
  @IsString()
  @IsOptional()
  message?: string;

  @ApiProperty({
    description: "Tag",
    required: false,
    example: "tag",
  })
  @IsString()
  @IsOptional()
  tag?: string;
}
