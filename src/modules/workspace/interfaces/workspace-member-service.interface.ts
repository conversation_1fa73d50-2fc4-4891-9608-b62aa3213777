import { AddWorkspaceMemberDto } from "../dto/add-workspace-member.dto";
import { UpdateWorkspaceMemberDto } from "../dto/update-workspace-member.dto";
import { WorkspaceMemberDocument } from "../entities/workspace-member.entity";

export interface WorkspaceMemberServiceInterface {
  addAdminMemberWhenCreatingWorkspace(
    workspaceId: string,
    userId: string
  ): Promise<WorkspaceMemberDocument>;

  /**
   * Add a user as a member to a workspace
   * @param workspaceId Workspace ID
   * @param addMemberDto User information to add
   * @param inviterId ID of the user sending the invitation
   */
  addMember(
    workspaceId: string,
    addMemberDto: AddWorkspaceMemberDto,
    inviterId: string
  ): Promise<WorkspaceMemberDocument>;

  /**
   * Update a member's role in a workspace
   * @param workspaceId Workspace ID
   * @param memberId Member ID to update
   * @param updateMemberDto Updated role information
   * @param userId User ID of the admin making the change
   */
  updateMember(
    workspaceId: string,
    memberId: string,
    updateMemberDto: UpdateWorkspaceMemberDto,
    userId: string
  ): Promise<WorkspaceMemberDocument>;

  /**
   * Remove a member from a workspace
   * @param workspaceId Workspace ID
   * @param memberId Member ID to remove
   * @param userId User ID of the admin making the change
   */
  removeMember(
    workspaceId: string,
    memberId: string,
    userId: string
  ): Promise<{ deleted: boolean }>;

  approveMember(
    workspaceId: string,
    memberId: string
  ): Promise<WorkspaceMemberDocument>;

  /**
   * Check if a user is a member of a workspace
   * @param workspaceId Workspace ID
   * @param userId User ID to check
   * @returns True if the user is a member of the workspace
   */
  isMember(workspaceId: string, userId: string): Promise<boolean>;

  getAccessibleWorkspaceIds(userId: string): Promise<string[]>;

  findOne(
    workspaceId: string,
    userId: string
  ): Promise<WorkspaceMemberDocument>;

  getOperators(workspaceId: string): Promise<WorkspaceMemberDocument[]>;
}
