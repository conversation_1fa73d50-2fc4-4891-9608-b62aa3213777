import { WorkspaceMemberInvitationDocument } from "../entities/workspace-member-invitation.entity";
import { WorkspaceMemberRole } from "../schemas/workspace-member.schema";

export interface WorkspaceMemberInvitationServiceInterface {
  createInvitation(
    workspaceId: string,
    userId: string,
    role: WorkspaceMemberRole
  ): Promise<WorkspaceMemberInvitationDocument>;

  acceptInvitation(token: string, userId: string): Promise<{ message: string }>;

  getInvitationByToken(
    token: string
  ): Promise<WorkspaceMemberInvitationDocument>;
}
