import { CreateWorkspaceDto } from "../dto/create-workspace.dto";
import {
  UpdateDefaultWorkspaceDto,
  UpdateWorkspaceDto,
} from "../dto/update-workspace.dto";
import { WorkspaceDocument } from "../entities/workspace.entity";

export interface WorkspaceServiceInterface {
  /**
   * Create a new workspace for a user
   * @param userId User ID who owns the workspace
   * @param createWorkspaceDto Workspace creation data
   */
  create(
    userId: string,
    createWorkspaceDto: CreateWorkspaceDto
  ): Promise<WorkspaceDocument>;

  /**
   * Find a workspace by ID without ownership check
   * @param id Workspace ID
   */
  findOneById(id: string): Promise<WorkspaceDocument>;

  /**
   * Update a workspace by ID, check ownership
   * @param id Workspace ID
   * @param userId User ID to check ownership
   * @param updateWorkspaceDto Updated workspace data
   */
  update(
    id: string,
    userId: string,
    updateWorkspaceDto: UpdateWorkspaceDto
  ): Promise<WorkspaceDocument>;

  /**
   * Delete a workspace by ID, check ownership
   * @param id Workspace ID
   * @param userId User ID to check ownership
   */
  remove(id: string, userId: string): Promise<{ deleted: boolean }>;

  /**
   * Create default workspace for a new user during registration
   * @param userId User ID
   * @param name User/Company name (optional)
   */
  createDefaultWorkspace(
    userId: string,
    name?: string
  ): Promise<WorkspaceDocument>;

  updateDefaultWorkspace(
    workspaceId: string,
    userId: string,
    updateWorkspaceDto: UpdateDefaultWorkspaceDto
  ): Promise<WorkspaceDocument>;

  /**
   * Find a workspace by Website ID
   * @param id Website ID
   */
  findOneByWebsiteId(id: string): Promise<WorkspaceDocument>;

  findWorkspacesByIds(ids: string[]): Promise<WorkspaceDocument[]>;

  getAccessibleWorkspaces(userId: string): Promise<WorkspaceDocument[]>;
}
