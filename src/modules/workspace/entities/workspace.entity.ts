import { Document, Types } from "mongoose";
import { WorkspaceType } from "../graphql/types/workspace.type";
import { UserDocument } from "@app/users/entities/user.entity";
import { WorkspaceStatus } from "../schemas/workspace.schema";

abstract class BaseWorkspace {
  abstract id: string;
  name!: string;
  websiteUrl!: string;
  websiteID!: string;
  logo?: string;
  contactEmail?: string;
  contactPhone?: string;
  owner?: UserDocument;
  ownerId?: string;
  status!: WorkspaceStatus;
  metadata?: Record<string, any>;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface WorkspaceDocument extends Document, Omit<BaseWorkspace, "id"> {
  _id: Types.ObjectId;
}

export class Workspace extends BaseWorkspace {
  id: string;

  constructor(workspaceDoc?: WorkspaceDocument) {
    super();
    if (workspaceDoc) {
      this.id = workspaceDoc._id.toString();
      this.name = workspaceDoc.name;
      this.websiteUrl = workspaceDoc.websiteUrl;
      this.websiteID = workspaceDoc.websiteID;
      this.logo = workspaceDoc.logo;
      this.contactEmail = workspaceDoc.contactEmail;
      this.contactPhone = workspaceDoc.contactPhone;
      this.ownerId = workspaceDoc.owner?._id.toString();
      this.status = workspaceDoc.status;
      this.metadata = workspaceDoc.metadata;
      this.createdAt = workspaceDoc.createdAt;
      this.updatedAt = workspaceDoc.updatedAt;
    }
  }

  toGraphQL(): WorkspaceType {
    const workspaceType = new WorkspaceType();
    workspaceType.id = this.id;
    workspaceType.name = this.name;
    workspaceType.websiteUrl = this.websiteUrl;
    workspaceType.websiteID = this.websiteID;
    workspaceType.logo = this.logo;
    workspaceType.contactEmail = this.contactEmail;
    workspaceType.contactPhone = this.contactPhone;
    workspaceType.ownerId = this.ownerId;
    workspaceType.status = this.status;
    workspaceType.metadata = this.metadata;
    return workspaceType;
  }
}
