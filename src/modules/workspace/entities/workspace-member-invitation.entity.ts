import { Document, Types } from "mongoose";
import { WorkspaceMemberRole } from "../schemas/workspace-member.schema";
import { WorkspaceDocument } from "./workspace.entity";
import { UserDocument } from "@app/users/entities/user.entity";

abstract class BaseWorkspaceMemberInvitation {
  abstract id: string;
  user?: UserDocument;
  userId?: string;
  workspace?: WorkspaceDocument;
  workspaceId?: string;
  token!: string;
  role!: WorkspaceMemberRole;
  expiredAt?: Date;
  isUsed?: boolean;
  createdAt?: Date;
}

export interface WorkspaceMemberInvitationDocument
  extends Document,
    Omit<BaseWorkspaceMemberInvitation, "id"> {
  _id: Types.ObjectId;
}

export class WorkspaceMemberInvitation extends BaseWorkspaceMemberInvitation {
  id: string;

  constructor(doc?: WorkspaceMemberInvitationDocument) {
    super();
    if (doc) {
      this.id = doc._id.toString();
      this.userId = (doc.user as UserDocument)?._id?.toString();
      this.workspaceId = (doc.workspace as WorkspaceDocument)?._id?.toString();
      this.role = doc.role;
      this.token = doc.token;
      this.createdAt = doc.createdAt;
      this.isUsed = doc.isUsed;
      this.expiredAt = doc.expiredAt;
    }
  }
}
