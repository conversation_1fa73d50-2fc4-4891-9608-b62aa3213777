import { Document, Types } from "mongoose";
import {
  WorkspaceMemberRole,
  WorkspaceMemberStatus,
} from "../schemas/workspace-member.schema";
import { UserDocument } from "../../users/entities/user.entity";
import { WorkspaceDocument } from "./workspace.entity";
import { WorkspaceMemberType } from "../graphql/types/workspace-member.type";

abstract class BaseWorkspaceMember {
  abstract id: string;
  user?: UserDocument;
  userId?: string;
  workspace?: WorkspaceDocument;
  workspaceId?: string;
  role!: WorkspaceMemberRole;
  status!: WorkspaceMemberStatus;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface WorkspaceMemberDocument
  extends Document,
    Omit<BaseWorkspaceMember, "id"> {
  _id: Types.ObjectId;
}

export class WorkspaceMember extends BaseWorkspaceMember {
  id: string;

  constructor(memberDoc?: WorkspaceMemberDocument) {
    super();
    if (memberDoc) {
      this.id = memberDoc._id.toString();
      this.userId = (memberDoc.user as UserDocument)?._id?.toString();
      this.workspaceId = (
        memberDoc.workspace as WorkspaceDocument
      )?._id?.toString();
      this.role = memberDoc.role;
      this.status = memberDoc.status;
      this.createdAt = memberDoc.createdAt;
      this.updatedAt = memberDoc.updatedAt;
    }
  }

  toGraphQL(): WorkspaceMemberType {
    const workspaceMemberType = new WorkspaceMemberType();
    workspaceMemberType.id = this.id;
    workspaceMemberType.userId = this.userId;
    workspaceMemberType.workspaceId = this.workspaceId;
    workspaceMemberType.role = this.role;
    workspaceMemberType.status = this.status;
    workspaceMemberType.createdAt = this.createdAt;
    workspaceMemberType.updatedAt = this.updatedAt;
    return workspaceMemberType;
  }
}
