import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Schema as MongooseSchema } from "mongoose";
import { User } from "../../users/schemas/user.schema";

export enum WorkspaceStatus {
  DRAFT = "draft",
  PENDING = "pending",
  INSTALLED = "installed",
  CLOSED = "closed",
}

@Schema({ timestamps: true })
export class Workspace {
  @Prop()
  name: string;

  @Prop()
  websiteUrl: string;

  @Prop({ required: true })
  websiteID: string;

  @Prop()
  logo: string;

  @Prop()
  contactEmail: string;

  @Prop()
  contactPhone: string;

  @Prop({
    type: String,
    enum: Object.values(WorkspaceStatus),
    default: WorkspaceStatus.DRAFT,
  })
  status: WorkspaceStatus;

  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User", required: true })
  owner: User;

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  metadata: Record<string, any>;
}

export const WorkspaceSchema = SchemaFactory.createForClass(Workspace);
