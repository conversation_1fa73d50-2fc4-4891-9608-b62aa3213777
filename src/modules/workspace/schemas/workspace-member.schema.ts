import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Schema as MongooseSchema } from "mongoose";
import { User } from "../../users/schemas/user.schema";
import { Workspace } from "./workspace.schema";

export enum WorkspaceMemberRole {
  ADMIN = "admin",
  DEVELOPER = "developer",
}

export enum WorkspaceMemberStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

@Schema({ timestamps: true })
export class WorkspaceMember {
  @Prop({ type: MongooseSchema.Types.ObjectId, ref: "User", required: true })
  user: User;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: true,
  })
  workspace: Workspace;

  @Prop({
    type: String,
    enum: Object.values(WorkspaceMemberRole),
    default: WorkspaceMemberRole.DEVELOPER,
  })
  role: WorkspaceMemberRole;

  @Prop({
    type: String,
    enum: Object.values(WorkspaceMemberStatus),
    default: WorkspaceMemberStatus.PENDING,
  })
  status: WorkspaceMemberStatus;
}

export const WorkspaceMemberSchema =
  SchemaFactory.createForClass(WorkspaceMember);

WorkspaceMemberSchema.index({ user: 1, workspace: 1 }, { unique: true });
