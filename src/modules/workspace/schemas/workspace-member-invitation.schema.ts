import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";
import { Schema as MongooseSchema } from "mongoose";
import { Workspace } from "./workspace.schema";
import { WorkspaceMemberRole } from "./workspace-member.schema";
import { User } from "@app/users/schemas/user.schema";

@Schema({ timestamps: true })
export class WorkspaceMemberInvitation {
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "User",
    required: true,
  })
  user: User;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: true,
  })
  workspace: Workspace;

  @Prop({ required: true })
  token: string;

  @Prop({ required: true })
  expiredAt: Date;

  @Prop({
    type: String,
    enum: Object.values(WorkspaceMemberRole),
    default: WorkspaceMemberRole.DEVELOPER,
  })
  role: WorkspaceMemberRole;

  @Prop({ default: false })
  isUsed: boolean;
}

export const WorkspaceMemberInvitationSchema = SchemaFactory.createForClass(
  WorkspaceMemberInvitation
);

WorkspaceMemberInvitationSchema.index(
  { user: 1, workspace: 1 },
  { unique: true }
);
