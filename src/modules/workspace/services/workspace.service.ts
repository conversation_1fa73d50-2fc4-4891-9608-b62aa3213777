import { forwardRef, Inject, Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { v4 as uuidv4 } from "uuid";
import { CreateWorkspaceDto } from "../dto/create-workspace.dto";
import {
  UpdateDefaultWorkspaceDto,
  UpdateWorkspaceDto,
} from "../dto/update-workspace.dto";
import { WorkspaceServiceInterface } from "../interfaces/workspace-service.interface";
import { I18nHelper } from "../../../common/utils/i18n.helper";
import { Workspace, WorkspaceDocument } from "../entities/workspace.entity";
import { WorkspaceMemberServiceInterface } from "../interfaces/workspace-member-service.interface";
import { WorkspaceStatus } from "../schemas/workspace.schema";

@Injectable()
export class WorkspaceService implements WorkspaceServiceInterface {
  constructor(
    @InjectModel(Workspace.name) private workspaceModel: Model<Workspace>,
    @Inject(forwardRef(() => "WorkspaceMemberServiceInterface"))
    private readonly workspaceMemberService: WorkspaceMemberServiceInterface
  ) {}

  private async createNewWorkspace(
    userId: string,
    createWorkspaceDto: CreateWorkspaceDto,
    status: WorkspaceStatus
  ): Promise<WorkspaceDocument> {
    const workspace = new this.workspaceModel({
      ...createWorkspaceDto,
      owner: userId,
      websiteID: uuidv4(), // Generate a unique website ID
      status: status,
    });

    await this.workspaceMemberService.addAdminMemberWhenCreatingWorkspace(
      workspace._id.toString(),
      userId
    );

    return workspace.save();
  }

  async create(
    userId: string,
    createWorkspaceDto: CreateWorkspaceDto
  ): Promise<WorkspaceDocument> {
    // Check if website URL already exists for this user
    const existingWorkspace = await this.workspaceModel
      .findOne({ owner: userId, websiteUrl: createWorkspaceDto.websiteUrl })
      .exec();

    if (existingWorkspace) {
      throw I18nHelper.badRequest("errors.workspaces.website_url_exists");
    }

    return this.createNewWorkspace(
      userId,
      createWorkspaceDto,
      WorkspaceStatus.PENDING
    );
  }

  /**
   * Find a workspace by ID without ownership check
   * @param id Workspace ID
   */
  async findOneById(id: string): Promise<WorkspaceDocument> {
    try {
      const workspace = await this.workspaceModel.findById(id).exec();

      if (!workspace) {
        throw I18nHelper.notFound("errors.workspaces.not_found");
      }

      return workspace;
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.find_failed");
    }
  }

  async update(
    id: string,
    userId: string,
    updateWorkspaceDto: UpdateWorkspaceDto
  ): Promise<WorkspaceDocument> {
    const workspace = await this.workspaceModel
      .findOneAndUpdate(
        { _id: id, owner: userId },
        { $set: updateWorkspaceDto },
        { new: true }
      )
      .exec();

    if (!workspace) {
      throw I18nHelper.notFound("errors.workspaces.not_found");
    }

    return workspace;
  }

  async remove(id: string, userId: string): Promise<{ deleted: boolean }> {
    try {
      const workspace = await this.workspaceModel
        .findOne({ _id: id, owner: userId })
        .exec();

      if (!workspace) {
        throw I18nHelper.notFound("errors.workspaces.not_found");
      }

      await this.workspaceModel.updateOne(
        { _id: id },
        { $set: { status: WorkspaceStatus.CLOSED } }
      );

      return { deleted: true };
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.delete_failed");
    }
  }

  async createDefaultWorkspace(
    userId: string,
    name?: string
  ): Promise<WorkspaceDocument> {
    return this.createNewWorkspace(
      userId,
      {
        name: name || "",
        websiteUrl: "",
      },
      WorkspaceStatus.DRAFT
    ); // Create a default workspace for the new user
  }

  async updateDefaultWorkspace(
    workspaceId: string,
    userId: string,
    updateWorkspaceDto: UpdateDefaultWorkspaceDto
  ): Promise<WorkspaceDocument> {
    const workspace = await this.workspaceModel
      .findOneAndUpdate(
        { _id: workspaceId, owner: userId },
        { $set: { ...updateWorkspaceDto, status: WorkspaceStatus.PENDING } },
        { new: true }
      )
      .exec();

    if (!workspace) {
      throw I18nHelper.notFound("errors.workspaces.not_found");
    }

    return workspace;
  }

  async findOneByWebsiteId(id: string): Promise<WorkspaceDocument> {
    const workspace = await this.workspaceModel
      .findOne({ websiteID: id })
      .exec();

    if (!workspace) {
      throw I18nHelper.notFound("errors.workspaces.not_found");
    }

    return workspace;
  }

  async findWorkspacesByIds(ids: string[]): Promise<WorkspaceDocument[]> {
    return this.workspaceModel.find({ _id: { $in: ids } }).exec();
  }

  async getAccessibleWorkspaces(userId: string): Promise<WorkspaceDocument[]> {
    const workspaceIds =
      await this.workspaceMemberService.getAccessibleWorkspaceIds(userId);
    return this.workspaceModel
      .find({
        _id: { $in: workspaceIds },
        status: { $ne: WorkspaceStatus.CLOSED },
      })
      .exec();
  }
}
