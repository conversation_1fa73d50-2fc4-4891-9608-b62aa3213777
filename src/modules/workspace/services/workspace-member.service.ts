import { Injectable, Inject } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import {
  WorkspaceMember,
  WorkspaceMemberRole,
  WorkspaceMemberStatus,
} from "../schemas/workspace-member.schema";
import { WorkspaceMemberServiceInterface } from "../interfaces/workspace-member-service.interface";
import { AddWorkspaceMemberDto } from "../dto/add-workspace-member.dto";
import { UpdateWorkspaceMemberDto } from "../dto/update-workspace-member.dto";
import { I18nHelper } from "../../../common/utils/i18n.helper";
import { UserServiceInterface } from "../../users/interfaces/user-service.interface";
import { WorkspaceMemberDocument } from "../entities/workspace-member.entity";
import { WorkspaceServiceInterface } from "../interfaces/workspace-service.interface";
import { UserDocument } from "@app/users";
import { WorkspaceMemberInvitationServiceInterface } from "../interfaces/workspace-member-invitation-service.interface";
import { MailService } from "src/mail";
import { UserStatus } from "@app/users/schemas/user.schema";

@Injectable()
export class WorkspaceMemberService implements WorkspaceMemberServiceInterface {
  constructor(
    @InjectModel(WorkspaceMember.name)
    private workspaceMemberModel: Model<WorkspaceMember>,
    @Inject("WorkspaceServiceInterface")
    private workspaceService: WorkspaceServiceInterface,
    @Inject("UserServiceInterface")
    private userService: UserServiceInterface,
    @Inject("WorkspaceMemberInvitationServiceInterface")
    private workspaceMemberInvitationService: WorkspaceMemberInvitationServiceInterface,
    private readonly mailService: MailService
  ) {}

  // Add a static translate method to I18nHelper since it's missing in the original
  private static translate(key: string): string {
    return key; // Just return the key as fallback
  }

  async addAdminMemberWhenCreatingWorkspace(
    workspaceId: string,
    userId: string
  ): Promise<WorkspaceMemberDocument> {
    try {
      // Add the user as an admin to the workspace
      const workspaceMember = new this.workspaceMemberModel({
        workspace: workspaceId,
        user: userId,
        role: WorkspaceMemberRole.ADMIN,
        status: WorkspaceMemberStatus.APRROVED,
      });

      return (await workspaceMember.save()) as unknown as WorkspaceMemberDocument;
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.add_admin_member_failed");
    }
  }

  async hasMembersManagementAccess(
    workspaceId: string,
    userId: string
  ): Promise<boolean> {
    try {
      // Check if user is the workspace owner
      const workspace = await this.workspaceService.findOneById(workspaceId);
      if (workspace?.owner) {
        const ownerId =
          workspace.owner instanceof Types.ObjectId
            ? workspace.owner.toString()
            : workspace.owner;

        if (ownerId === userId) {
          return true; // Owner has all permissions
        }
      }

      // Check if user is admin role
      const member = await this.workspaceMemberModel
        .findOne({
          workspace: workspaceId,
          user: userId,
          status: WorkspaceMemberStatus.APRROVED,
          role: WorkspaceMemberRole.ADMIN,
        })
        .exec();

      if (member) {
        return true;
      }

      return false;
    } catch (error) {
      console.log(error);
      return false;
    }
  }

  async addMember(
    workspaceId: string,
    addMemberDto: AddWorkspaceMemberDto,
    inviterId: string
  ): Promise<WorkspaceMemberDocument> {
    const hasAccess = await this.hasMembersManagementAccess(
      workspaceId,
      inviterId
    );
    if (!hasAccess) {
      throw I18nHelper.unauthorized("errors.workspaces.unauthorized");
    }

    let user: UserDocument | null;
    user = await this.userService.findByEmail(addMemberDto.email);
    console.log(user);

    if (user) {
      // Check if user is already a member
      const existingMember = await this.workspaceMemberModel
        .findOne({
          workspace: workspaceId,
          user: user._id,
        })
        .exec();

      console.log(workspaceId);

      console.log(existingMember);

      if (existingMember) {
        throw I18nHelper.conflict("errors.workspaces.user_already_member");
      }
    } else {
      user = await this.userService.createTempUser(addMemberDto.email);
    }

    const workspace = await this.workspaceService.findOneById(workspaceId);
    if (!workspace) {
      throw I18nHelper.notFound("errors.workspaces.not_found");
    }
    const workspaceMember = new this.workspaceMemberModel({
      workspace: workspaceId,
      user: user._id,
      role: addMemberDto.role,
    });
    const savedMember = await workspaceMember.save();

    const invitation =
      await this.workspaceMemberInvitationService.createInvitation(
        workspaceId,
        user._id.toString(),
        addMemberDto.role || WorkspaceMemberRole.DEVELOPER
      );

    const inviter = await this.userService.findOne(inviterId);
    if (!inviter) {
      throw I18nHelper.notFound("errors.users.not_found");
    }

    await this.mailService.sendInvitationEmail(
      user.email,
      workspace.name ?? "",
      inviter.firstName || "",
      invitation.token
    );

    return savedMember as unknown as WorkspaceMemberDocument;
  }

  async approveMember(
    workspaceId: string,
    memberId: string
  ): Promise<WorkspaceMemberDocument> {
    try {
      const user = await this.userService.findOne(memberId);
      if (!user) {
        throw I18nHelper.notFound("errors.users.not_found");
      }

      // Find the workspace member
      const member = await this.workspaceMemberModel
        .findOne({ user: user._id, workspace: workspaceId })
        .exec();
      if (!member) {
        throw I18nHelper.notFound("errors.workspaces.member_not_found");
      }

      // Update member status
      member.status = WorkspaceMemberStatus.APRROVED;
      return (await member.save()) as unknown as WorkspaceMemberDocument;
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.approve_member_failed");
    }
  }

  async updateMember(
    workspaceId: string,
    memberId: string,
    updateMemberDto: UpdateWorkspaceMemberDto,
    userId: string
  ): Promise<WorkspaceMemberDocument> {
    try {
      const hasAccess = await this.hasMembersManagementAccess(
        workspaceId,
        userId
      );
      if (!hasAccess) {
        throw I18nHelper.unauthorized("errors.workspaces.unauthorized");
      }

      const member = await this.workspaceMemberModel
        .findByIdAndUpdate(memberId, updateMemberDto, { new: true })
        .exec();

      return member as unknown as WorkspaceMemberDocument;
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.update_member_failed");
    }
  }

  async removeMember(
    workspaceId: string,
    memberId: string,
    userId: string
  ): Promise<{ deleted: boolean }> {
    try {
      const hasAccess = await this.hasMembersManagementAccess(
        workspaceId,
        userId
      );
      if (!hasAccess) {
        throw I18nHelper.unauthorized("errors.workspaces.unauthorized");
      }

      // Check if member exists
      const member = await this.workspaceMemberModel.findById(memberId).exec();
      if (!member) {
        throw I18nHelper.notFound("errors.workspaces.member_not_found");
      }

      // Delete the member
      await this.workspaceMemberModel.deleteOne({ _id: memberId }).exec();
      return { deleted: true };
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.remove_member_failed");
    }
  }

  async isMember(workspaceId: string, userId: string): Promise<boolean> {
    try {
      const isMember = await this.workspaceMemberModel
        .findOne({
          workspace: workspaceId,
          user: userId,
          status: WorkspaceMemberStatus.APRROVED,
        })
        .exec();

      return !!isMember; // Convert to boolean
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.find_members_failed");
    }
  }

  async getAccessibleWorkspaceIds(userId: string): Promise<string[]> {
    const workspaces = await this.workspaceMemberModel
      .find({
        user: userId,
        status: WorkspaceMemberStatus.APRROVED,
      })
      .distinct("workspace")
      .exec();
    return workspaces.map((workspace) => workspace as unknown as string);
  }

  async findOne(
    workspaceId: string,
    userId: string
  ): Promise<WorkspaceMemberDocument> {
    try {
      const member = await this.workspaceMemberModel
        .findOne({
          workspace: workspaceId,
          user: userId,
        })
        .exec();

      return member as unknown as WorkspaceMemberDocument;
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.find_member_failed");
    }
  }

  async getOperators(workspaceId: string): Promise<WorkspaceMemberDocument[]> {
    try {
      const members = await this.workspaceMemberModel
        .find({
          workspace: workspaceId,
        })
        .exec();

      return members as unknown as WorkspaceMemberDocument[];
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest("errors.workspaces.find_member_failed");
    }
  }
}
