import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { WorkspaceMemberRole } from "../schemas/workspace-member.schema";
import { I18nHelper } from "../../../common/utils/i18n.helper";
import { WorkspaceMemberInvitationServiceInterface } from "../interfaces/workspace-member-invitation-service.interface";
import { WorkspaceMemberInvitation } from "../schemas/workspace-member-invitation.schema";
import { WorkspaceMemberInvitationDocument } from "../entities/workspace-member-invitation.entity";
import * as crypto from "crypto";
import { WorkspaceMemberServiceInterface } from "../interfaces/workspace-member-service.interface";
import { WorkspaceDocument } from "../entities/workspace.entity";

@Injectable()
export class WorkspaceMemberInvitationService
  implements WorkspaceMemberInvitationServiceInterface
{
  constructor(
    @InjectModel(WorkspaceMemberInvitation.name)
    private workspaceMemberInvitationModel: Model<WorkspaceMemberInvitation>,
    @Inject(forwardRef(() => "WorkspaceMemberServiceInterface"))
    private workspaceMemberService: WorkspaceMemberServiceInterface
  ) {}

  async createInvitation(
    workspaceId: string,
    userId: string,
    role: WorkspaceMemberRole
  ): Promise<WorkspaceMemberInvitationDocument> {
    try {
      // no need to validate workspace exists, it's validated in the services use it
      const token = crypto.randomBytes(32).toString("hex");
      const expiredAt = new Date(Date.now() + 3 * 24 * 60 * 60 * 1000); // 3 days

      const invitation = new this.workspaceMemberInvitationModel({
        workspace: workspaceId,
        user: userId,
        role,
        token,
        expiredAt,
      });

      const savedInvitation = await invitation.save();
      return savedInvitation as unknown as WorkspaceMemberInvitationDocument;
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest(
        "errors.workspaces.invitation_creation_failed"
      );
    }
  }

  async acceptInvitation(
    token: string,
    userId: string
  ): Promise<{ message: string }> {
    try {
      const invitation = await this.workspaceMemberInvitationModel
        .findOne({ token, user: userId })
        .exec();
      if (!invitation) {
        throw new NotFoundException("Token is invalid");
      }

      if (invitation.isUsed) {
        throw I18nHelper.badRequest(
          "errors.workspaces.invitation_already_used"
        );
      }

      if (invitation.expiredAt < new Date()) {
        throw I18nHelper.badRequest("errors.workspaces.invitation_expired");
      }

      // Mark invitation as used
      invitation.isUsed = true;
      await invitation.save();

      await this.workspaceMemberService.approveMember(
        (invitation.workspace as unknown as WorkspaceDocument)._id.toString(),
        userId
      );

      return { message: "Invitation accepted successfully" };
    } catch (error) {
      console.log(error);
      throw I18nHelper.badRequest(
        "errors.workspaces.invitation_acceptance_failed"
      );
    }
  }

  async getInvitationByToken(
    token: string
  ): Promise<WorkspaceMemberInvitationDocument> {
    const invitation = await this.workspaceMemberInvitationModel
      .findOne({ token })
      .exec();
    if (!invitation) {
      throw new NotFoundException("Token is invalid");
    }
    if (invitation.expiredAt < new Date()) {
      throw I18nHelper.badRequest("errors.workspaces.invitation_expired");
    }
    if (invitation.isUsed) {
      throw I18nHelper.badRequest("errors.workspaces.invitation_already_used");
    }

    return invitation as unknown as WorkspaceMemberInvitationDocument;
  }
}
