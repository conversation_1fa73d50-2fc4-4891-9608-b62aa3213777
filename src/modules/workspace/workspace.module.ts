import { forward<PERSON><PERSON>, <PERSON>du<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { WorkspaceController } from "./controllers/workspace.controller";
import { WorkspaceService } from "./services/workspace.service";
import { Workspace, WorkspaceSchema } from "./schemas/workspace.schema";
import {
  WorkspaceMember,
  WorkspaceMemberSchema,
} from "./schemas/workspace-member.schema";
import { UsersModule } from "../users/users.module";
import { WorkspaceMemberService } from "./services/workspace-member.service";
import { WorkspaceInvitationController } from "./controllers/workspace-invitation.controller";
import { MailModule } from "../../mail/mail.module";
import { WorkspaceResolver } from "./graphql/resolvers/workspace.resolver";
import { LoaderModule } from "../graphql/loaders/loader.module";
import { RedisModule } from "src/services/redis/redis.module";
import {
  WorkspaceMemberInvitation,
  WorkspaceMemberInvitationSchema,
} from "./schemas/workspace-member-invitation.schema";
import { WorkspaceMemberInvitationService } from "./services/workspace-member-invitation.service";
import { AuthModule } from "../auth/auth.module";
import { WorkspaceMemberFieldResolver } from "./graphql/resolvers/workspace-member-field.resolver";
import { WorkspaceMemberController } from "./controllers/workspace-member.controller";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Workspace.name, schema: WorkspaceSchema },
      { name: WorkspaceMember.name, schema: WorkspaceMemberSchema },
      {
        name: WorkspaceMemberInvitation.name,
        schema: WorkspaceMemberInvitationSchema,
      },
    ]),
    forwardRef(() => UsersModule),
    forwardRef(() => AuthModule),
    MailModule,
    LoaderModule,
    RedisModule,
  ],
  controllers: [
    WorkspaceController,
    WorkspaceInvitationController,
    WorkspaceMemberController,
  ],
  providers: [
    {
      provide: "WorkspaceServiceInterface",
      useClass: WorkspaceService,
    },
    {
      provide: "WorkspaceMemberServiceInterface",
      useClass: WorkspaceMemberService,
    },
    {
      provide: "WorkspaceMemberInvitationServiceInterface",
      useClass: WorkspaceMemberInvitationService,
    },
    WorkspaceResolver,
    WorkspaceMemberFieldResolver,
  ],
  exports: [
    "WorkspaceServiceInterface",
    "WorkspaceMemberServiceInterface",
    "WorkspaceMemberInvitationServiceInterface",
    WorkspaceResolver,
    WorkspaceMemberFieldResolver,
  ],
})
export class WorkspaceModule {}
