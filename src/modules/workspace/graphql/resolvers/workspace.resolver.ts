import { <PERSON><PERSON><PERSON>, <PERSON>ry, <PERSON><PERSON><PERSON><PERSON><PERSON>, Pa<PERSON> } from "@nestjs/graphql";
import { Inject, UseGuards } from "@nestjs/common";

import { User<PERSON>oader } from "src/modules/graphql/loaders/user.loader";
import { WorkspaceType } from "../types/workspace.type";
import { WorkspaceServiceInterface } from "../../interfaces/workspace-service.interface";
import { GqlAuthGuard } from "src/modules/auth/guards/gql-auth.guard";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { UserType } from "@app/users/graphql/types/user.type";
import { JwtPayload } from "src/modules/auth/interfaces/jwt-payload.interface";
import { Workspace } from "../../entities/workspace.entity";
import { WorkspaceMemberServiceInterface } from "../../interfaces/workspace-member-service.interface";
import { WorkspaceMemberRole } from "../../schemas/workspace-member.schema";
import { toGlobalId } from "graphql-relay";
import { WorkspaceMember } from "../../entities/workspace-member.entity";
import { WorkspaceMemberType } from "../types/workspace-member.type";

@Resolver(() => WorkspaceType)
export class WorkspaceResolver {
  constructor(
    @Inject("WorkspaceServiceInterface")
    private readonly workspaceService: WorkspaceServiceInterface,
    @Inject("WorkspaceMemberServiceInterface")
    private readonly workspaceMemberService: WorkspaceMemberServiceInterface,
    @Inject(UserLoader) private readonly userLoader: UserLoader
  ) {}

  @ResolveField(() => String)
  id(@Parent() item: Workspace) {
    return toGlobalId("Workspace", item.id);
  }

  @ResolveField(() => String)
  rawId(@Parent() item: Workspace) {
    return item.id;
  }

  @Query(() => [WorkspaceType])
  @UseGuards(GqlAuthGuard)
  async workspaces(@CurrentUser() user: JwtPayload): Promise<WorkspaceType[]> {
    const userId = user.sub;
    const items = await this.workspaceService.getAccessibleWorkspaces(userId);
    return items.map((item) => new Workspace(item).toGraphQL());
  }

  @ResolveField(() => UserType, { nullable: true })
  async owner(@Parent() parent: WorkspaceType): Promise<UserType | null> {
    if (parent.ownerId != null) {
      return this.userLoader.loadUserWithWorkspace({
        userId: parent.ownerId,
        workspaceId: parent.id,
      }) as Promise<UserType>;
    } else {
      return null;
    }
  }

  @ResolveField(() => WorkspaceMemberRole, { nullable: true })
  async role(
    @Parent() parent: WorkspaceType,
    @CurrentUser() user: JwtPayload
  ): Promise<WorkspaceMemberRole | null> {
    if (parent.id != null && user.sub != null) {
      const member = await this.workspaceMemberService.findOne(
        parent.id,
        user.sub
      );
      if (member) {
        return member.role;
      } else {
        return null;
      }
    } else {
      return null;
    }
  }

  @Query(() => [WorkspaceMemberType])
  @UseGuards(GqlAuthGuard)
  async operators(
    @CurrentUser() user: JwtPayload
  ): Promise<WorkspaceMemberType[]> {
    const workspaceId = user.workspaceId;
    const items = await this.workspaceMemberService.getOperators(workspaceId);
    return items.map((item) => new WorkspaceMember(item).toGraphQL());
  }
}
