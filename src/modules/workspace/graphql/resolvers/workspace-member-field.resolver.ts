import { <PERSON><PERSON><PERSON>, ResolveField, Pa<PERSON> } from "@nestjs/graphql";
import { toGlobalId } from "graphql-relay";
import { WorkspaceMember } from "../../entities/workspace-member.entity";
import { WorkspaceMemberType } from "../types/workspace-member.type";
import { UserType } from "@app/users/graphql/types/user.type";
import { UserLoader } from "src/modules/graphql/loaders/user.loader";
import { Inject } from "@nestjs/common";
import { WorkspaceType } from "../types/workspace.type";
import { WorkspaceLoader } from "src/modules/graphql/loaders/workspace.loader";

@Resolver(() => WorkspaceMemberType)
export class WorkspaceMemberFieldResolver {
  constructor(
    @Inject(UserLoader) private readonly userLoader: UserLoader,
    @Inject(WorkspaceLoader) private readonly workspaceLoader: WorkspaceLoader
  ) {}

  @ResolveField(() => String)
  id(@Parent() item: WorkspaceMember) {
    return toGlobalId("WorkspaceMember", item.id);
  }

  @ResolveField(() => String)
  rawId(@Parent() item: WorkspaceMember) {
    return item.id;
  }

  @ResolveField(() => UserType, { nullable: true })
  async user(@Parent() parent: WorkspaceMemberType): Promise<UserType | null> {
    console.log(parent);
    if (parent.userId != null) {
      return this.userLoader.loadUserWithWorkspace({
        userId: parent.userId,
        workspaceId: parent.workspaceId ?? "",
      }) as Promise<UserType>;
    } else {
      return null;
    }
  }

  @ResolveField(() => WorkspaceType, { nullable: true })
  async workspace(
    @Parent() parent: WorkspaceMemberType
  ): Promise<WorkspaceType | null> {
    if (parent.workspaceId != null) {
      return this.workspaceLoader.batchWorkspaces.load(
        parent.workspaceId
      ) as Promise<WorkspaceType>;
    } else {
      return null;
    }
  }
}
