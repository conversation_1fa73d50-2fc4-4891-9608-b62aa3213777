import { UserType } from "@app/users/graphql/types/user.type";
import { Field, ID, ObjectType, registerEnumType } from "@nestjs/graphql";
import { ConnectionType } from "src/modules/graphql/types/connection.type";
import { Node } from "src/modules/graphql/types/node.interface";
import { WorkspaceMemberRole } from "../../schemas/workspace-member.schema";
import GraphQLJSON from "graphql-type-json";
import { WorkspaceStatus } from "../../schemas/workspace.schema";

registerEnumType(WorkspaceStatus, {
  name: "WorkspaceStatus",
  description: "Status of a workspace",
});

registerEnumType(WorkspaceMemberRole, {
  name: "WorkspaceMemberRole",
  description: "Type of member",
});

@ObjectType("Workspace", { implements: [Node] })
export class WorkspaceType implements Node {
  @Field(() => ID)
  id: string;

  @Field(() => String)
  name: string;

  @Field(() => String)
  websiteUrl: string;

  @Field(() => String)
  websiteID: string;

  @Field(() => String, { nullable: true })
  logo?: string;

  @Field(() => String, { nullable: true })
  contactEmail?: string;

  @Field(() => String, { nullable: true })
  contactPhone?: string;

  @Field(() => UserType, { nullable: true })
  owner?: UserType;

  // Hidden field to store the owner ID for the resolver
  ownerId?: string;

  @Field(() => WorkspaceStatus, { nullable: true })
  status?: WorkspaceStatus;

  @Field(() => WorkspaceMemberRole, { nullable: true })
  role?: WorkspaceMemberRole;

  @Field(() => GraphQLJSON, { nullable: true })
  metadata?: Record<string, any>;
}

@ObjectType()
export class WorkspaceConnection extends ConnectionType(WorkspaceType) {}
