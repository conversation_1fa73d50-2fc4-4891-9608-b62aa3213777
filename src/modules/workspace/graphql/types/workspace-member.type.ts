import { UserType } from "@app/users/graphql/types/user.type";
import { Field, ID, ObjectType, registerEnumType } from "@nestjs/graphql";
import { ConnectionType } from "src/modules/graphql/types/connection.type";
import { Node } from "src/modules/graphql/types/node.interface";
import {
  WorkspaceMemberRole,
  WorkspaceMemberStatus,
} from "../../schemas/workspace-member.schema";
import { WorkspaceType } from "./workspace.type";

registerEnumType(WorkspaceMemberRole, {
  name: "WorkspaceMemberRole",
  description: "Type of member",
});

registerEnumType(WorkspaceMemberStatus, {
  name: "WorkspaceMemberStatus",
  description: "Status of a workspace member",
});

@ObjectType("WorkspaceMember", { implements: [Node] })
export class WorkspaceMemberType implements Node {
  @Field(() => ID)
  id: string;

  @Field(() => UserType, { nullable: true })
  user?: UserType;

  // Hidden field to store the user ID for the resolver
  userId?: string;

  @Field(() => WorkspaceType, { nullable: true })
  workspace?: WorkspaceType;

  // Hidden field to store the workspace ID for the resolver
  workspaceId?: string;

  @Field(() => WorkspaceMemberRole, { nullable: true })
  role?: WorkspaceMemberRole;

  @Field(() => WorkspaceMemberStatus, { nullable: true })
  status?: WorkspaceMemberStatus;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;
}

@ObjectType()
export class WorkspaceMemberConnection extends ConnectionType(
  WorkspaceMemberType
) {}
