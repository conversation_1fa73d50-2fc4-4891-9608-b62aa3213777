import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Inject,
  Request,
  Patch,
} from "@nestjs/common";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import { WorkspaceMemberServiceInterface } from "../interfaces/workspace-member-service.interface";
import { AddWorkspaceMemberDto } from "../dto/add-workspace-member.dto";
import { UpdateWorkspaceMemberDto } from "../dto/update-workspace-member.dto";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { WorkspaceMember } from "../entities/workspace-member.entity";

@ApiTags("Workspace Members")
@Controller("workspaces/members")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WorkspaceMemberController {
  constructor(
    @Inject("WorkspaceMemberServiceInterface")
    private readonly workspaceMemberService: WorkspaceMemberServiceInterface
  ) {}

  @Post()
  @ApiOperation({ summary: "Add a member to a workspace" })
  @ApiResponse({ status: 201, description: "Member added successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "Workspace or user not found" })
  async addMember(
    @Body() addMemberDto: AddWorkspaceMemberDto,
    @Request() req: RequestWithUser
  ): Promise<WorkspaceMember> {
    const member = await this.workspaceMemberService.addMember(
      req.user?.workspaceId ?? "",
      addMemberDto,
      req.user.sub
    );
    return new WorkspaceMember(member);
  }

  @Patch(":memberId")
  @ApiOperation({ summary: "Update a member's role" })
  @ApiParam({ name: "memberId", description: "Member ID" })
  @ApiResponse({ status: 200, description: "Member updated successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "Workspace or member not found" })
  async updateMember(
    @Param("memberId") memberId: string,
    @Body() updateMemberDto: UpdateWorkspaceMemberDto,
    @Request() req: RequestWithUser
  ) {
    const member = await this.workspaceMemberService.updateMember(
      req.user?.workspaceId ?? "",
      memberId,
      updateMemberDto,
      req.user.sub
    );
    return new WorkspaceMember(member);
  }

  @Delete(":memberId")
  @ApiOperation({ summary: "Remove a member from a workspace" })
  @ApiParam({ name: "memberId", description: "Member ID" })
  @ApiResponse({ status: 200, description: "Member removed successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "Workspace or member not found" })
  removeMember(
    @Param("memberId") memberId: string,
    @Request() req: RequestWithUser
  ) {
    return this.workspaceMemberService.removeMember(
      req.user?.workspaceId ?? "",
      memberId,
      req.user.sub
    );
  }
}
