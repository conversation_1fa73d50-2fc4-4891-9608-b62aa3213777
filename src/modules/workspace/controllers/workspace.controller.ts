import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Inject,
  Request,
  Put,
  Patch,
} from "@nestjs/common";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import { WorkspaceServiceInterface } from "../interfaces/workspace-service.interface";
import { CreateWorkspaceDto } from "../dto/create-workspace.dto";
import { UpdateWorkspaceDto } from "../dto/update-workspace.dto";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { Workspace } from "../entities/workspace.entity";

@ApiTags("Workspaces")
@Controller("workspaces")
export class WorkspaceController {
  constructor(
    @Inject("WorkspaceServiceInterface")
    private readonly workspaceService: WorkspaceServiceInterface
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Create a new workspace" })
  @ApiResponse({ status: 201, description: "Workspace created successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async create(
    @Body() createWorkspaceDto: CreateWorkspaceDto,
    @Request() req: RequestWithUser
  ): Promise<Workspace> {
    try {
      const workspace = await this.workspaceService.create(
        req.user.sub,
        createWorkspaceDto
      );
      return new Workspace(workspace);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  @Patch(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update workspace" })
  @ApiParam({ name: "id", description: "Workspace ID" })
  @ApiResponse({ status: 200, description: "Workspace updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "Workspace not found" })
  async update(
    @Param("id") id: string,
    @Body() updateWorkspaceDto: UpdateWorkspaceDto,
    @Request() req: RequestWithUser
  ): Promise<Workspace> {
    try {
      const workspace = await this.workspaceService.update(
        id,
        req.user.sub,
        updateWorkspaceDto
      );
      return new Workspace(workspace);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Delete workspace" })
  @ApiParam({ name: "id", description: "Workspace ID" })
  @ApiResponse({ status: 200, description: "Workspace deleted successfully" })
  @ApiResponse({
    status: 400,
    description: "Cannot delete primary workspace if it's the only one",
  })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "Workspace not found" })
  remove(@Param("id") id: string, @Request() req: RequestWithUser) {
    return this.workspaceService.remove(id, req.user.sub);
  }

  @Get("guest/by-website/:websiteID")
  @ApiOperation({ summary: "Get workspace info by websiteID (guest, no auth)" })
  @ApiParam({ name: "websiteID", description: "Website ID" })
  @ApiResponse({
    status: 200,
    description: "Workspace details",
  })
  @ApiResponse({ status: 404, description: "Workspace not found" })
  async getWorkspaceByWebsiteID(
    @Param("websiteID") websiteID: string
  ): Promise<Workspace> {
    try {
      const workspace =
        await this.workspaceService.findOneByWebsiteId(websiteID);
      return new Workspace(workspace);
    } catch (error) {
      console.log(error);
      throw error;
    }
  }
}
