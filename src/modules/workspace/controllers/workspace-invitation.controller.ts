import { <PERSON>, Get, Query, Inject, UseGuards, Req } from "@nestjs/common";
import {
  ApiBearerAuth,
  <PERSON>pi<PERSON>peration,
  Api<PERSON><PERSON>y,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { Request } from "express";
import { JwtAuthGuard } from "src/modules/auth/guards/jwt-auth.guard";
import { OptionalJwtAuthGuard } from "src/modules/auth/guards/optional-jwt-auth.guard";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { WorkspaceMemberInvitationServiceInterface } from "../interfaces/workspace-member-invitation-service.interface";
import { WorkspaceServiceInterface } from "../interfaces/workspace-service.interface";
import { Workspace } from "../entities/workspace.entity";
import { UserStatus } from "@app/users/schemas/user.schema";
import { UserServiceInterface } from "@app/users";

@ApiTags("Workspace Invitations")
@Controller("workspaces/invitations")
export class WorkspaceInvitationController {
  constructor(
    @Inject("WorkspaceMemberInvitationServiceInterface")
    private readonly workspaceMemberInvitationService: WorkspaceMemberInvitationServiceInterface,
    @Inject("WorkspaceServiceInterface")
    private readonly workspaceService: WorkspaceServiceInterface,
    @Inject("UserServiceInterface")
    private readonly userService: UserServiceInterface
  ) {}

  @Get("check")
  @UseGuards(OptionalJwtAuthGuard)
  @ApiOperation({ summary: "Get a workspace invitation by token" })
  @ApiQuery({ name: "token", description: "Invitation token" })
  @ApiResponse({ status: 200, description: "Invitation details" })
  @ApiResponse({ status: 400, description: "Invalid invitation token" })
  async getInvitationByToken(
    @Query("token") token: string,
    @Req() req: Request
  ): Promise<{
    token: string;
    workspace: Workspace;
    role: string;
    hasAccount: boolean;
    isCurrentUserInvited?: boolean;
  }> {
    const invitation =
      await this.workspaceMemberInvitationService.getInvitationByToken(token);
    const workspace = await this.workspaceService.findOneById(
      invitation.workspace!._id.toString()
    );
    const user = await this.userService.findOne(
      invitation.user!._id.toString()
    );
    if (!user) {
      throw new Error("User not found");
    }
    const hasAccount = user.status !== UserStatus.PENDING;

    // Check if user is authenticated and get additional info
    let isCurrentUserInvited: boolean | undefined = undefined;
    if (req.user) {
      isCurrentUserInvited =
        (req as RequestWithUser).user.sub === invitation.user!._id.toString();
    }

    return {
      token: invitation.token,
      workspace: new Workspace(workspace),
      role: invitation.role,
      hasAccount: hasAccount,
      isCurrentUserInvited: isCurrentUserInvited,
    };
  }

  @Get("accept")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Accept a workspace invitation" })
  @ApiQuery({ name: "token", description: "Invitation token" })
  @ApiResponse({ status: 200, description: "Invitation accepted successfully" })
  @ApiResponse({
    status: 400,
    description: "Invalid or expired invitation token",
  })
  @ApiResponse({ status: 404, description: "Invitation not found" })
  async acceptInvitation(
    @Query("token") token: string,
    @Req() req: RequestWithUser
  ) {
    await this.workspaceMemberInvitationService.acceptInvitation(
      token,
      req.user.sub
    );
    return {
      message: "Invitation accepted successfully",
    };
  }
}
