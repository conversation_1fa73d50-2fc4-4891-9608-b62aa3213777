import { ApiProperty } from "@nestjs/swagger";
import { IsEmail, IsEnum, IsNotEmpty, IsOptional } from "class-validator";
import { WorkspaceMemberRole } from "../schemas/workspace-member.schema";

export class AddWorkspaceMemberDto {
  @ApiProperty({
    description: "Email of the user to add to the workspace",
    example: "<EMAIL>",
  })
  @IsEmail({}, { message: "errors.validation.invalid_email" })
  @IsNotEmpty({ message: "errors.validation.email_required" })
  email: string;

  @ApiProperty({
    description: "Role of the user in the workspace",
    example: "developer",
    enum: WorkspaceMemberRole,
    default: WorkspaceMemberRole.DEVELOPER,
  })
  @IsEnum(WorkspaceMemberRole, {
    message: "errors.validation.invalid_workspace_role",
  })
  @IsOptional()
  role?: WorkspaceMemberRole;
}
