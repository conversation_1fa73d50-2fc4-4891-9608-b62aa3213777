import { IsNotEmpty, <PERSON>String, IsUrl } from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class CreateWorkspaceDto {
  @ApiProperty({
    description: "Workspace name",
    example: "My Company",
  })
  @IsString({ message: "errors.validation.name_must_be_string" })
  @IsNotEmpty({ message: "errors.validation.name_required" })
  name: string;

  @ApiProperty({
    description: "Workspace website URL",
    example: "https://mycompany.com",
  })
  @IsUrl({}, { message: "errors.validation.website_url_invalid" })
  @IsNotEmpty({ message: "errors.validation.website_url_required" })
  websiteUrl: string;
}
