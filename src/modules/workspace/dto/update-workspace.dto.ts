import {
  Is<PERSON><PERSON>,
  <PERSON><PERSON>ptional,
  <PERSON>String,
  IsUrl,
  IsObject,
} from "class-validator";
import { ApiProperty } from "@nestjs/swagger";

export class UpdateDefaultWorkspaceDto {
  name?: string;
  websiteUrl: string;
}

export class UpdateWorkspaceDto {
  @ApiProperty({
    description: "Workspace name",
    example: "My Company",
    required: false,
  })
  @IsString({ message: "errors.validation.name_must_be_string" })
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: "Workspace logo URL",
    example: "https://storage.cloud.com/logos/mylogo.png",
    required: false,
  })
  @IsUrl({}, { message: "errors.validation.logo_url_invalid" })
  @IsOptional()
  logo?: string;

  @ApiProperty({
    description: "Contact email",
    example: "<EMAIL>",
    required: false,
  })
  @IsEmail({}, { message: "errors.validation.contact_email_invalid" })
  @IsOptional()
  contactEmail?: string;

  @ApiProperty({
    description: "Contact phone number",
    example: "1234567890",
    required: false,
  })
  @IsString({ message: "errors.validation.contact_phone_must_be_string" })
  @IsOptional()
  contactPhone?: string;

  @ApiProperty({
    description: "Workspace metadata",
    example: {
      key: "Value",
    },
    required: false,
  })
  @IsObject({ message: "errors.validation.metadata_must_be_object" })
  @IsOptional()
  metadata?: Record<string, any>;
}
