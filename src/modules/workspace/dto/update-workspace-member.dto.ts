import { ApiProperty } from "@nestjs/swagger";
import { IsEnum, IsNotEmpty } from "class-validator";
import {
  WorkspaceMemberRole,
  WorkspaceMemberStatus,
} from "../schemas/workspace-member.schema";

export class UpdateWorkspaceMemberDto {
  @ApiProperty({
    description: "Role of the user in the workspace",
    example: "developer",
    enum: WorkspaceMemberRole,
  })
  @IsEnum(WorkspaceMemberRole, {
    message: "errors.validation.invalid_workspace_role",
  })
  @IsNotEmpty({ message: "errors.validation.role_required" })
  role: WorkspaceMemberRole;

  @ApiProperty({
    description: "Status of the user in the workspace",
    example: "approved",
    enum: WorkspaceMemberStatus,
  })
  @IsEnum(WorkspaceMemberStatus, {
    message: "errors.validation.invalid_workspace_status",
  })
  @IsNotEmpty({ message: "errors.validation.status_required" })
  status: WorkspaceMemberStatus;
}
