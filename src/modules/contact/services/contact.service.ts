import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { ContactServiceInterface } from "../interfaces/contact-service.interface";
import { Contact, ContactDocument } from "../entities/contact.entity";
import { CreateContactDto } from "../dto/create-contact.dto";
import { UpdateContactDto } from "../dto/update-contact.dto";
import { PaginationArgs } from "src/modules/graphql/types/pagination.args";
import { UserStatusRedisService } from "src/services/redis/user-status.redis.service";
import { ContactContext, ContactType } from "../schemas/contact.schema";
import { ConnectionArgs } from "src/modules/graphql/types/connection.args";
import { ContactConnection } from "../graphql/types/contact.type";
import { fromGlobalId, toGlobalId } from "graphql-relay";

@Injectable()
export class ContactService implements ContactServiceInterface {
  constructor(
    @InjectModel("Contact")
    private contactModel: Model<Contact>,
    private readonly userStatusRedisService: UserStatusRedisService
  ) {}

  async createContact(
    workspaceId: string,
    createDto: CreateContactDto
  ): Promise<ContactDocument> {
    return this.contactModel.create({
      ...createDto,
      workspace: new Types.ObjectId(workspaceId),
    });
  }

  async updateContact(
    id: string,
    updateDto: UpdateContactDto
  ): Promise<ContactDocument> {
    const updatedContact = await this.contactModel
      .findByIdAndUpdate(id, updateDto, { new: true })
      .exec();

    if (!updatedContact) {
      throw new Error(`Contact with ID ${id} not found`);
    }

    return updatedContact;
  }

  async updateContactContext(
    id: string,
    context: ContactContext
  ): Promise<ContactDocument> {
    const updatedContact = await this.contactModel
      .findByIdAndUpdate(
        id,
        [
          {
            $set: {
              context: {
                $mergeObjects: ["$context", context],
              },
            },
          },
        ],
        { new: true }
      )
      .exec();

    if (!updatedContact) {
      throw new Error(`Contact with ID ${id} not found`);
    }

    return updatedContact;
  }

  async removeContact(id: string, userId: string): Promise<boolean> {
    const result = await this.contactModel
      .updateOne({ _id: id }, { deleted: true })
      .exec();
    return result !== null;
  }

  async removeContacts(
    ids: string[],
    userId: string,
    workspaceId?: string
  ): Promise<{ deletedCount: number; failedIds: string[] }> {
    const failedIds: string[] = [];
    let deletedCount = 0;

    // Process deletions in batches to avoid overwhelming the database
    const batchSize = 10;
    for (let i = 0; i < ids.length; i += batchSize) {
      const batch = ids.slice(i, i + batchSize);

      try {
        // Build query with workspace validation if provided
        const query = { _id: { $in: batch }, deleted: { $ne: true } };
        if (workspaceId) {
          query["workspace"] = new Types.ObjectId(workspaceId);
        }

        const result = await this.contactModel
          .updateMany(query, { deleted: true })
          .exec();

        deletedCount += result.modifiedCount;

        // Check which IDs in this batch failed to delete
        if (result.modifiedCount < batch.length) {
          const deletedIds = await this.contactModel
            .find({ _id: { $in: batch }, deleted: true })
            .select("_id")
            .exec();

          const deletedIdStrings = deletedIds.map((doc) => doc._id.toString());
          const batchFailedIds = batch.filter(
            (id) => !deletedIdStrings.includes(id)
          );
          failedIds.push(...batchFailedIds);
        }
      } catch (error) {
        // If the entire batch fails, add all IDs to failed list
        failedIds.push(...batch);
      }
    }

    return { deletedCount, failedIds };
  }

  private buildContactQuery(
    workspaceId: string,
    keyword?: string
  ): Record<string, any> {
    const query: Record<string, any> = {
      workspace: new Types.ObjectId(workspaceId),
      type: ContactType.PERMANENT,
      deleted: { $ne: true },
    };

    if (keyword) {
      query["$or"] = [
        { name: { $regex: keyword, $options: "i" } },
        { email: { $regex: keyword, $options: "i" } },
        { phoneNumber: { $regex: keyword, $options: "i" } },
      ];
    }

    return query;
  }

  async getContacts(
    workspaceId: string,
    args: PaginationArgs,
    keyword?: string
  ): Promise<ContactDocument[]> {
    const { first, offset, last } = args;
    const limit = first ?? last ?? 10;
    const skip = offset ?? 0;

    return this.contactModel
      .find(this.buildContactQuery(workspaceId, keyword))
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .exec();
  }

  countContacts(workspaceId: string, keyword?: string): Promise<number> {
    return this.contactModel
      .find(this.buildContactQuery(workspaceId, keyword))
      .countDocuments()
      .exec();
  }

  async countLiveContacts(
    workspaceId: string
  ): Promise<{ count: number; active: number }> {
    // TODO: active is contacts are online and visiting your site, count is online contacts
    const query: Record<string, any> = {
      workspace: new Types.ObjectId(workspaceId),
      type: ContactType.PERMANENT,
      deleted: { $ne: true },
      "context.latitude": { $exists: true, $ne: null },
      "context.longitude": { $exists: true, $ne: null },
    };

    const count = await this.contactModel.find(query).countDocuments().exec();
    const active =
      await this.userStatusRedisService.countOnlineContacts(workspaceId);

    return { count, active };
  }

  async getLiveContacts(
    workspaceId: string,
    args: ConnectionArgs
  ): Promise<ContactConnection> {
    const { first, after, before, last } = args;

    if (before || last) {
      throw new Error("not supported");
    }

    const limit = first ?? 10;

    const onlineContactIds =
      await this.userStatusRedisService.getOnlineContacts(workspaceId);

    if (onlineContactIds.length === 0) {
      return {
        edges: [],
        pageInfo: {
          hasNextPage: false,
          hasPreviousPage: false,
          startCursor: null,
          endCursor: null,
        },
      };
    }

    const query: Record<string, any> = { _id: { $in: onlineContactIds } };

    if (after) {
      query._id = {
        $in: onlineContactIds,
        $lt: new Types.ObjectId(fromGlobalId(after).id),
      };
    }

    const sort: Record<string, any> = { _id: -1 };

    let contacts = await this.contactModel
      .find(query)
      .sort(sort)
      .limit(limit + 1); // +1 to check hasNext/hasPrevious

    const hasExtraItem = contacts.length > limit;
    if (hasExtraItem) contacts = contacts.slice(0, limit);

    const edges = contacts.map((item) => ({
      node: new Contact(item).toGraphQL(),
      cursor: toGlobalId("Contact", item._id.toString()),
    }));

    const startCursor = edges[0]?.cursor;
    const endCursor = edges[edges.length - 1]?.cursor;

    return {
      edges,
      pageInfo: {
        hasNextPage: hasExtraItem,
        hasPreviousPage: after !== null,
        startCursor,
        endCursor,
      },
    };
  }

  async getContact(id: string): Promise<ContactDocument> {
    const contact = await this.contactModel.findById(id).exec();
    if (!contact) {
      throw new Error(`Contact with ID ${id} not found`);
    }
    return contact;
  }

  async findContactsByIds(ids: string[]): Promise<ContactDocument[]> {
    return this.contactModel.find({ _id: { $in: ids } }).exec();
  }

  async createTempContact(
    workspaceId: string,
    guestId: string
  ): Promise<ContactDocument> {
    const contact = await this.findContactByGuestId(workspaceId, guestId);
    if (!contact) {
      const newContact = await this.contactModel.create({
        guestId: guestId,
        workspace: new Types.ObjectId(workspaceId),
        name: "Guest",
        type: ContactType.TEMPORARY,
      });
      return newContact;
    } else {
      return contact;
    }
  }

  async findContactByGuestId(
    workspaceId: string,
    guestId: string
  ): Promise<ContactDocument | null> {
    return this.contactModel
      .findOne({
        guestId: guestId,
        workspace: new Types.ObjectId(workspaceId),
      })
      .exec();
  }

  async findContactsByEmail(email: string): Promise<ContactDocument[]> {
    return this.contactModel.find({ email }).exec();
  }
}
