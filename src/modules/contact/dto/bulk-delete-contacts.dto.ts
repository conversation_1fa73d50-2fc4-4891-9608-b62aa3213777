import { ApiProperty } from "@nestjs/swagger";
import { IsArray, IsString, ArrayMinSize, ArrayMaxSize } from "class-validator";

export class BulkDeleteContactsDto {
  @ApiProperty({
    description: "Array of contact IDs to delete",
    example: ["60f1b2b3c4d5e6f7a8b9c0d1", "60f1b2b3c4d5e6f7a8b9c0d2"],
    type: [String],
  })
  @IsArray()
  @ArrayMinSize(1, { message: "At least one contact ID must be provided" })
  @ArrayMaxSize(100, { message: "Cannot delete more than 100 contacts at once" })
  @IsString({ each: true, message: "Each contact ID must be a string" })
  contactIds: string[];
}
