import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsEmail,
  IsOptional,
  IsBoolean,
  IsObject,
  IsArray,
  IsEnum,
  IsDate,
  IsNumber,
  ValidateNested,
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
} from "class-validator";
import { Type } from "class-transformer";
import { ContactGender } from "../schemas/contact.schema";

// Custom validator to check for unknown keys
function IsValidCompanyInfo(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      name: "isValidCompanyInfo",
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      validator: {
        validate(value: any, _args: ValidationArguments) {
          if (!value || typeof value !== "object") {
            return true; // Let other validators handle type checking
          }

          const allowedKeys = [
            "company",
            "jobTitle",
            "jobRole",
            "website",
            "city",
            "country",
            "employees",
          ];
          const providedKeys = Object.keys(value);
          const invalidKeys = providedKeys.filter(
            (key) => !allowedKeys.includes(key)
          );

          return invalidKeys.length === 0;
        },
        defaultMessage(args: ValidationArguments) {
          const value = args.value;
          if (value && typeof value === "object" && value !== null) {
            const allowedKeys = [
              "company",
              "jobTitle",
              "jobRole",
              "website",
              "city",
              "country",
              "employees",
            ];
            const providedKeys = Object.keys(value);
            const invalidKeys = providedKeys.filter(
              (key) => !allowedKeys.includes(key)
            );

            if (invalidKeys.length > 0) {
              return `Invalid keys in companyInfo: ${invalidKeys.join(", ")}. Allowed keys are: ${allowedKeys.join(", ")}`;
            }
          }
          return "Invalid companyInfo structure";
        },
      },
    });
  };
}

export class CompanyInfoDto {
  @ApiProperty({
    description: "Company name",
    required: false,
    example: "Acme Corp",
  })
  @IsString()
  @IsOptional()
  company?: string;

  @ApiProperty({
    description: "Job title of the contact",
    required: false,
    example: "Senior Manager",
  })
  @IsString()
  @IsOptional()
  jobTitle?: string;

  @ApiProperty({
    description: "Job role of the contact",
    required: false,
    example: "Sales Manager",
  })
  @IsString()
  @IsOptional()
  jobRole?: string;

  @ApiProperty({
    description: "Company website",
    required: false,
    example: "https://acmecorp.com",
  })
  @IsString()
  @IsOptional()
  website?: string;

  @ApiProperty({
    description: "Company city",
    required: false,
    example: "New York",
  })
  @IsString()
  @IsOptional()
  city?: string;

  @ApiProperty({
    description: "Company country",
    required: false,
    example: "United States",
  })
  @IsString()
  @IsOptional()
  country?: string;

  @ApiProperty({
    description: "Number of employees in the company",
    required: false,
    example: 500,
  })
  @IsNumber()
  @IsOptional()
  employees?: number;
}

export class UpdateContactDto {
  @ApiProperty({
    description: "Avatar of the contact",
    required: false,
  })
  @IsString()
  @IsOptional()
  avatar?: string;

  @ApiProperty({
    description: "Name of the contact",
    required: false,
    example: "John Doe",
  })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({
    description: "Email address of the contact",
    required: false,
    example: "<EMAIL>",
  })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({
    description: "Phone number of the contact",
    required: false,
    example: "+1234567890",
  })
  @IsString()
  @IsOptional()
  phoneNumber?: string;

  @ApiProperty({
    description: "Address of the contact",
    required: false,
    example: "123 Main St, City, Country",
  })
  @IsString()
  @IsOptional()
  address?: string;

  @ApiProperty({
    description: "Website of the contact",
    required: false,
    example: "https://google.com",
  })
  @IsString()
  @IsOptional()
  website?: string;

  @ApiProperty({
    description: "Gender of the contact",
    required: false,
    enum: ContactGender,
    example: ContactGender.MALE,
  })
  @IsEnum(ContactGender)
  @IsOptional()
  gender?: ContactGender;

  @ApiProperty({
    description: "Whether to send notifications to this contact",
    required: false,
    default: false,
  })
  @IsBoolean()
  @IsOptional()
  notification?: boolean;

  @ApiProperty({
    description: "Company information of the contact",
    required: false,
    type: CompanyInfoDto,
  })
  @IsValidCompanyInfo({
    message:
      "Invalid keys in companyInfo. Allowed keys are: company, jobTitle, jobRole, website, city, country, employees",
  })
  @ValidateNested()
  @Type(() => CompanyInfoDto)
  @IsOptional()
  companyInfo?: CompanyInfoDto;

  @ApiProperty({
    description: "Segments the contact belongs to",
    required: false,
    example: ["VIP", "Enterprise"],
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  segments?: string[];

  @ApiProperty({
    description: "Additional metadata for the contact",
    required: false,
    example: {
      source: "Website",
      lastPurchase: "2024-01-01",
    },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;

  @ApiProperty({
    description: "Additional notes about the contact",
    required: false,
    example: "Important client, prefers email communication",
  })
  @IsString()
  @IsOptional()
  notes?: string;
}
