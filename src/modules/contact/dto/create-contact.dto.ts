import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsEmail, IsNotEmpty } from "class-validator";

export class CreateContactDto {
  @ApiProperty({
    description: "Name of the contact",
    required: true,
    example: "<PERSON>",
  })
  @IsString()
  name!: string;

  @ApiProperty({
    description: "Email address of the contact",
    required: true,
    example: "<EMAIL>",
  })
  @IsEmail()
  email!: string;
}
