import { forwardRef, Modu<PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { ContactController } from "./controllers/contact.controller";
import { ContactService } from "./services/contact.service";
import { Contact, ContactSchema } from "./schemas/contact.schema";
import { WorkspaceModule } from "../workspace/workspace.module";
import { LoaderModule } from "../graphql/loaders/loader.module";
import { ContactResolver } from "./graphql/resolvers/contact.resolver";
import { RedisModule } from "src/services/redis/redis.module";
import { ContactFieldResolver } from "./graphql/resolvers/contact-field.resolver";
import { ChatModule } from "../chat/chat.module";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: Contact.name, schema: ContactSchema }]),
    forwardRef(() => WorkspaceModule),
    forwardRef(() => LoaderModule),
    RedisModule,
    forwardRef(() => ChatModule),
  ],
  controllers: [ContactController],
  providers: [
    {
      provide: "ContactServiceInterface",
      useClass: ContactService,
    },
    ContactResolver,
    ContactFieldResolver,
  ],
  exports: ["ContactServiceInterface", ContactResolver, ContactFieldResolver],
})
export class ContactModule {}
