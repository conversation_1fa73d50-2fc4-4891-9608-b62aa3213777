import { Prop, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Schema as MongooseSchema } from "mongoose";
import { Workspace } from "src/modules/workspace/schemas/workspace.schema";

export enum ContactGender {
  MALE = "male",
  FEMALE = "female",
}

export enum ContactType {
  TEMPORARY = "temporary",
  PERMANENT = "permanent",
}

export interface ContactContext {
  ip?: string;
  countryCode?: string;
  countryName?: string;
  city?: string;
  region?: string;
  latitude?: number;
  longitude?: number;
  timezone?: string;
  browser?: string;
  os?: string;
  language?: string;
}

export interface CompanyInfo {
  company?: string;
  jobTitle?: string;
  jobRole?: string;
  website?: string;
  city?: string;
  country?: string;
  employees?: number;
}

@Schema({ timestamps: true })
export class Contact {
  @Prop()
  guestId: string;

  @Prop({ required: false })
  avatar?: string;

  @Prop({ required: true })
  name: string;

  @Prop()
  email?: string;

  @Prop()
  phoneNumber?: string;

  @Prop()
  address?: string;

  @Prop({ required: false })
  website?: string;

  @Prop({
    type: String,
    enum: Object.values(ContactGender),
  })
  gender: ContactGender;

  @Prop({
    type: String,
    enum: Object.values(ContactType),
    default: ContactType.PERMANENT,
  })
  type: ContactType;

  @Prop({ default: false })
  notification: boolean;

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  companyInfo: CompanyInfo;

  @Prop({ type: [String], default: [] })
  segments: string[];

  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  metadata: Record<string, any>;

  @Prop({ type: MongooseSchema.Types.Mixed })
  context?: ContactContext;

  @Prop()
  notes?: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: true,
  })
  workspace: Workspace;

  @Prop({ default: false })
  deleted: boolean;
}

export const ContactSchema = SchemaFactory.createForClass(Contact);
