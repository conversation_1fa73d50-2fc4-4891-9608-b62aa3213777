import { Document, Types } from "mongoose";
import {
  ContactContext,
  ContactGender,
  CompanyInfo,
} from "../schemas/contact.schema";
import { WorkspaceDocument } from "src/modules/workspace/entities/workspace.entity";
import { ContactType } from "../graphql/types/contact.type";
import {
  BaseDocument,
  BaseEntity,
} from "src/modules/common/entities/base.entity";

abstract class BaseContact extends BaseEntity {
  guestId?: string;
  avatar?: string;
  name!: string;
  email?: string;
  phoneNumber?: string;
  address?: string;
  website?: string;
  gender?: ContactGender;
  type?: ContactType;
  notification: boolean;
  companyInfo: CompanyInfo;
  segments: string[];
  metadata: Record<string, any>;
  context?: ContactContext;
  notes?: string;
  isOnline: boolean;
  lastActivityAt?: Date;
  workspace?: WorkspaceDocument;
  workspaceId?: string;
}

export interface ContactDocument extends BaseDocument<BaseContact> {
  _id: Types.ObjectId;
}

export class Contact extends BaseContact {
  constructor(contactDoc?: ContactDocument) {
    super();
    if (contactDoc) {
      super.assignFields(contactDoc);

      this.guestId = contactDoc.guestId;
      this.avatar = contactDoc.avatar;
      this.name = contactDoc.name;
      this.email = contactDoc.email;
      this.phoneNumber = contactDoc.phoneNumber;
      this.address = contactDoc.address;
      this.website = contactDoc.website;
      this.gender = contactDoc.gender;
      this.type = contactDoc.type;
      this.notification = contactDoc.notification;
      this.companyInfo = contactDoc.companyInfo;
      this.segments = contactDoc.segments;
      this.metadata = contactDoc.metadata;
      this.context = contactDoc.context;
      this.notes = contactDoc.notes;
      this.isOnline = contactDoc.isOnline;
      this.lastActivityAt = contactDoc.lastActivityAt;
      this.workspaceId = contactDoc.workspace?._id.toString();
    }
  }

  toGraphQL(): ContactType {
    const contactType = new ContactType();
    contactType.id = this.id;
    contactType.guestId = this.guestId;
    contactType.avatar = this.avatar;
    contactType.name = this.name;
    contactType.email = this.email;
    contactType.phoneNumber = this.phoneNumber;
    contactType.address = this.address;
    contactType.website = this.website;
    contactType.gender = this.gender;
    contactType.notification = this.notification;
    contactType.companyInfo = this.companyInfo;
    contactType.segments = this.segments;
    contactType.metadata = this.metadata;
    contactType.context = this.context;
    contactType.notes = this.notes;
    contactType.isOnline = this.isOnline;
    contactType.lastActivityAt = this.lastActivityAt;
    contactType.createdAt = this.createdAt;
    contactType.updatedAt = this.updatedAt;
    contactType.workspaceId = this.workspaceId;

    return contactType;
  }
}
