import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Inject,
  Request,
  Put,
  Query,
} from "@nestjs/common";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import { WorkspacePluginServiceInterface } from "../interfaces/workspace-plugin-service.interface";
import { InstallPluginDto } from "../dto/install-plugin.dto";
import { UpdatePluginSettingsDto } from "../dto/update-plugin-settings.dto";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
  ApiQuery,
} from "@nestjs/swagger";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";

@ApiTags("Workspace Plugins")
@Controller("workspaces/:workspaceId/plugins")
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class WorkspacePluginController {
  constructor(
    @Inject("WorkspacePluginServiceInterface")
    private readonly workspacePluginService: WorkspacePluginServiceInterface
  ) {}

  @Post()
  @ApiOperation({ summary: "Install a plugin in the workspace" })
  @ApiParam({ name: "workspaceId", description: "Workspace ID" })
  @ApiResponse({ status: 201, description: "Plugin installed successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  install(
    @Param("workspaceId") workspaceId: string,
    @Body() installDto: InstallPluginDto,
    @Request() req: RequestWithUser
  ) {
    return this.workspacePluginService.install(
      workspaceId,
      req.user.sub,
      installDto
    );
  }

  @Delete(":pluginId")
  @ApiOperation({ summary: "Uninstall a plugin from the workspace" })
  @ApiParam({ name: "workspaceId", description: "Workspace ID" })
  @ApiParam({ name: "pluginId", description: "Plugin ID or key" })
  @ApiResponse({ status: 200, description: "Plugin uninstalled successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  uninstall(
    @Param("workspaceId") workspaceId: string,
    @Param("pluginId") pluginId: string,
    @Request() req: RequestWithUser
  ) {
    return this.workspacePluginService.uninstall(
      workspaceId,
      req.user.sub,
      pluginId
    );
  }

  @Put(":pluginId/enable")
  @ApiOperation({ summary: "Enable a plugin" })
  @ApiParam({ name: "workspaceId", description: "Workspace ID" })
  @ApiParam({ name: "pluginId", description: "Plugin ID or key" })
  @ApiResponse({ status: 200, description: "Plugin enabled successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  enable(
    @Param("workspaceId") workspaceId: string,
    @Param("pluginId") pluginId: string,
    @Request() req: RequestWithUser
  ) {
    return this.workspacePluginService.enable(
      workspaceId,
      req.user.sub,
      pluginId
    );
  }

  @Put(":pluginId/disable")
  @ApiOperation({ summary: "Disable a plugin" })
  @ApiParam({ name: "workspaceId", description: "Workspace ID" })
  @ApiParam({ name: "pluginId", description: "Plugin ID or key" })
  @ApiResponse({ status: 200, description: "Plugin disabled successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  disable(
    @Param("workspaceId") workspaceId: string,
    @Param("pluginId") pluginId: string,
    @Request() req: RequestWithUser
  ) {
    return this.workspacePluginService.disable(
      workspaceId,
      req.user.sub,
      pluginId
    );
  }

  @Put(":pluginId/settings")
  @ApiOperation({ summary: "Update plugin settings" })
  @ApiParam({ name: "workspaceId", description: "Workspace ID" })
  @ApiParam({ name: "pluginId", description: "Plugin ID or key" })
  @ApiResponse({ status: 200, description: "Settings updated successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  updateSettings(
    @Param("workspaceId") workspaceId: string,
    @Param("pluginId") pluginId: string,
    @Body() updateDto: UpdatePluginSettingsDto,
    @Request() req: RequestWithUser
  ) {
    return this.workspacePluginService.updateSettings(
      workspaceId,
      req.user.sub,
      pluginId,
      updateDto
    );
  }

  @Get()
  @ApiOperation({ summary: "Get all plugins in the workspace" })
  @ApiParam({ name: "workspaceId", description: "Workspace ID" })
  @ApiResponse({ status: 200, description: "List of workspace plugins" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  findAll(
    @Param("workspaceId") workspaceId: string,
    @Request() req: RequestWithUser
  ) {
    return this.workspacePluginService.findAllByWorkspace(
      workspaceId,
      req.user.sub
    );
  }

  @Get(":pluginId")
  @ApiOperation({ summary: "Get a specific plugin in the workspace" })
  @ApiParam({ name: "workspaceId", description: "Workspace ID" })
  @ApiParam({ name: "pluginId", description: "Plugin ID or key" })
  @ApiResponse({ status: 200, description: "Plugin details" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({
    status: 403,
    description: "Forbidden - Insufficient permissions",
  })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  findOne(
    @Param("workspaceId") workspaceId: string,
    @Param("pluginId") pluginId: string,
    @Request() req: RequestWithUser
  ) {
    return this.workspacePluginService.findOne(
      workspaceId,
      req.user.sub,
      pluginId
    );
  }

  @Post(":pluginId/execute")
  @ApiOperation({ summary: "Execute a plugin action" })
  @ApiParam({ name: "workspaceId", description: "Workspace ID" })
  @ApiParam({ name: "pluginId", description: "Plugin ID or key" })
  @ApiQuery({ name: "action", description: "Action to execute" })
  @ApiResponse({ status: 200, description: "Action executed successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  executeAction(
    @Param("workspaceId") workspaceId: string,
    @Param("pluginId") pluginId: string,
    @Query("action") action: string,
    @Body() params: Record<string, any>
  ) {
    return this.workspacePluginService.executePluginAction(
      workspaceId,
      pluginId,
      action,
      params
    );
  }
}
