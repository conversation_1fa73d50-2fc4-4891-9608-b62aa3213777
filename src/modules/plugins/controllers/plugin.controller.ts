import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Inject,
  Put,
} from "@nestjs/common";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import { AdminGuard } from "../../auth/guards/admin.guard";
import { PluginServiceInterface } from "../interfaces/plugin-service.interface";
import { CreatePluginDto } from "../dto/create-plugin.dto";
import {
  ApiBearerAuth,
  ApiOperation,
  ApiParam,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";

@ApiTags("Plugins")
@Controller("plugins")
export class PluginController {
  constructor(
    @Inject("PluginServiceInterface")
    private readonly pluginService: PluginServiceInterface
  ) {}

  @Post()
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Register a new plugin (admin only)" })
  @ApiResponse({ status: 201, description: "Plugin registered successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden - Admin only" })
  registerPlugin(@Body() createPluginDto: CreatePluginDto) {
    return this.pluginService.registerPlugin(createPluginDto);
  }

  @Get()
  @ApiOperation({ summary: "Get all available plugins" })
  @ApiResponse({ status: 200, description: "List of all plugins" })
  findAll() {
    return this.pluginService.findAll();
  }

  @Get(":id")
  @ApiOperation({ summary: "Get a plugin by ID" })
  @ApiParam({ name: "id", description: "Plugin ID or key" })
  @ApiResponse({ status: 200, description: "Plugin details" })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  findOne(@Param("id") id: string) {
    // Try to find by ID first, if that fails, try by key
    return this.pluginService.findById(id).catch(() => {
      return this.pluginService.findByKey(id);
    });
  }

  @Put(":id")
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update a plugin (admin only)" })
  @ApiParam({ name: "id", description: "Plugin ID" })
  @ApiResponse({ status: 200, description: "Plugin updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden - Admin only" })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  update(
    @Param("id") id: string,
    @Body() updatePluginDto: Partial<CreatePluginDto>
  ) {
    return this.pluginService.update(id, updatePluginDto);
  }

  @Delete(":id")
  @UseGuards(JwtAuthGuard, AdminGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Delete a plugin (admin only)" })
  @ApiParam({ name: "id", description: "Plugin ID" })
  @ApiResponse({ status: 200, description: "Plugin deleted successfully" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  @ApiResponse({ status: 403, description: "Forbidden - Admin only" })
  @ApiResponse({ status: 404, description: "Plugin not found" })
  remove(@Param("id") id: string) {
    return this.pluginService.remove(id);
  }
}
