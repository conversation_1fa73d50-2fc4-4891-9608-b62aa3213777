import { <PERSON><PERSON>, <PERSON>hem<PERSON>, <PERSON><PERSON>aFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";
import { ApiProperty } from "@nestjs/swagger";

export type PluginDocument = Plugin & Document;

/**
 * Available plugin categories
 */
export enum PluginCategory {
  INTEGRATION = "integration",
  WORKFLOW = "workflow",
  ANALYTICS = "analytics",
  MARKETING = "marketing",
  COMMUNICATION = "communication",
  UTILITY = "utility",
  OTHER = "other",
}

/**
 * Plugin schema that defines the structure of a plugin in the system
 */
@Schema({ timestamps: true })
export class Plugin {
  @ApiProperty({
    description: "Plugin unique identifier",
    example: "shopify-integration",
  })
  @Prop({ required: true, unique: true })
  key: string;

  @ApiProperty({
    description: "Plugin name",
    example: "Shopify Integration",
  })
  @Prop({ required: true })
  name: string;

  @ApiProperty({
    description: "Plugin description",
    example: "Integrates your chat with Shopify store data and events",
  })
  @Prop({ required: true })
  description: string;

  @ApiProperty({
    description: "Plugin version",
    example: "1.0.0",
  })
  @Prop({ required: true })
  version: string;

  @ApiProperty({
    description: "Plugin author",
    example: "OnlyChat Team",
  })
  @Prop({ required: true })
  author: string;

  @ApiProperty({
    description: "Plugin category",
    enum: PluginCategory,
    example: PluginCategory.INTEGRATION,
  })
  @Prop({ required: true, type: String, enum: PluginCategory })
  category: PluginCategory;

  @ApiProperty({
    description: "Plugin icon URL",
    example: "https://storage.cloud.com/plugins/shopify-icon.png",
  })
  @Prop()
  iconUrl: string;

  @ApiProperty({
    description: "Link to plugin documentation",
    example: "https://docs.onlychat.com/plugins/shopify",
  })
  @Prop()
  documentationUrl: string;

  @ApiProperty({
    description: "Main entry point for the plugin (relative path)",
    example: "src/plugins/shopify/index.js",
  })
  @Prop({ required: true })
  entryPoint: string;

  @ApiProperty({
    description: "Whether the plugin is globally available for all workspaces",
    example: true,
  })
  @Prop({ default: true })
  isGloballyAvailable: boolean;

  @ApiProperty({
    description: "Whether the plugin is system plugin (cannot be uninstalled)",
    example: false,
  })
  @Prop({ default: false })
  isSystem: boolean;

  @ApiProperty({
    description: "Plugin settings schema (JSON Schema format)",
    example: {
      type: "object",
      properties: {
        apiKey: { type: "string" },
        shopDomain: { type: "string" },
      },
      required: ["apiKey", "shopDomain"],
    },
  })
  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  settingsSchema: Record<string, any>;

  @ApiProperty({
    description: "Default plugin settings",
    example: {
      webhookEnabled: true,
    },
  })
  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  defaultSettings: Record<string, any>;

  @ApiProperty({
    description: "Required permissions for this plugin",
    example: ["chat:read", "chat:write", "contacts:read"],
  })
  @Prop({ type: [String], default: [] })
  requiredPermissions: string[];
}

export const PluginSchema = SchemaFactory.createForClass(Plugin);
