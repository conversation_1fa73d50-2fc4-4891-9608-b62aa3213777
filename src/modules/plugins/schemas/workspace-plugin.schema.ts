import { <PERSON><PERSON>, <PERSON>hem<PERSON>, <PERSON><PERSON><PERSON>Factory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";
import { ApiProperty } from "@nestjs/swagger";
import { Workspace } from "../../workspace/schemas/workspace.schema";
import { Plugin } from "./plugin.schema";

export type WorkspacePluginDocument = WorkspacePlugin & Document;

/**
 * Status of a plugin installation
 */
export enum PluginInstallationStatus {
  ACTIVE = "active",
  DISABLED = "disabled",
  ERROR = "error",
  PENDING = "pending",
}

/**
 * Schema to track plugin installations per workspace
 */
@Schema({ timestamps: true })
export class WorkspacePlugin {
  @ApiProperty({
    description: "The workspace this plugin is installed in",
  })
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: true,
  })
  workspace: Workspace;

  @ApiProperty({
    description: "The plugin that is installed",
  })
  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Plugin",
    required: true,
  })
  plugin: Plugin;

  @ApiProperty({
    description: "Current installation status",
    enum: PluginInstallationStatus,
    example: PluginInstallationStatus.ACTIVE,
  })
  @Prop({
    type: String,
    enum: PluginInstallationStatus,
    default: PluginInstallationStatus.ACTIVE,
  })
  status: PluginInstallationStatus;

  @ApiProperty({
    description: "Custom plugin settings for this workspace",
    example: {
      apiKey: "sk_123456",
      shopDomain: "mystore.shopify.com",
      webhookEnabled: true,
    },
  })
  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  settings: Record<string, any>;

  @ApiProperty({
    description: "Error message if plugin is in error state",
    example: "Failed to connect to Shopify API: Invalid credentials",
  })
  @Prop()
  errorMessage: string;

  @ApiProperty({
    description: "Installation date",
    example: "2023-08-15T14:30:00.000Z",
  })
  @Prop({ type: Date, default: Date.now })
  installedAt: Date;

  @ApiProperty({
    description: "Last time plugin settings were updated",
    example: "2023-08-16T10:15:00.000Z",
  })
  @Prop({ type: Date })
  lastUpdatedAt: Date;

  @ApiProperty({
    description: "Usage statistics for this plugin",
    example: {
      totalExecutions: 156,
      lastExecutedAt: "2023-08-16T15:20:00.000Z",
      averageExecutionTime: 120,
    },
  })
  @Prop({ type: MongooseSchema.Types.Mixed, default: {} })
  usage: Record<string, any>;
}

export const WorkspacePluginSchema =
  SchemaFactory.createForClass(WorkspacePlugin);

// Add a compound index to ensure uniqueness of plugin per workspace
WorkspacePluginSchema.index({ workspace: 1, plugin: 1 }, { unique: true });
