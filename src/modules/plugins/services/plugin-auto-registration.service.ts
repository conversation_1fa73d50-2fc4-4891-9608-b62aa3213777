import { Injectable, OnModuleInit, Inject } from "@nestjs/common";
import { PluginServiceInterface } from "../interfaces/plugin-service.interface";
import { CreatePluginDto } from "../dto/create-plugin.dto";
import * as fs from "fs";
import * as path from "path";

@Injectable()
export class PluginAutoRegistrationService implements OnModuleInit {
  constructor(
    @Inject("PluginServiceInterface")
    private readonly pluginService: PluginServiceInterface
  ) {}

  async onModuleInit() {
    console.log("🔍 Scanning for plugins to auto-register...");
    await this.scanAndRegisterPlugins();
  }

  private async scanAndRegisterPlugins() {
    try {
      const pluginsDir = path.join(process.cwd(), "src", "plugins");

      if (!fs.existsSync(pluginsDir)) {
        console.log(
          "📁 No plugins directory found, skipping auto-registration"
        );
        return;
      }

      const pluginDirs = fs
        .readdirSync(pluginsDir, { withFileTypes: true })
        .filter((dirent) => dirent.isDirectory())
        .map((dirent) => dirent.name);

      console.log(
        `📦 Found ${pluginDirs.length} plugin directories: ${pluginDirs.join(", ")}`
      );

      for (const pluginDir of pluginDirs) {
        await this.registerPluginFromDirectory(pluginDir);
      }

      console.log("✅ Plugin auto-registration completed");
    } catch (error) {
      console.error("❌ Error during plugin auto-registration:", error);
    }
  }

  private async registerPluginFromDirectory(pluginDir: string) {
    try {
      const pluginPath = path.join(process.cwd(), "src", "plugins", pluginDir);
      const indexPath = path.join(pluginPath, "index.ts");

      if (!fs.existsSync(indexPath)) {
        console.log(`⚠️  No index.ts found for plugin: ${pluginDir}, skipping`);
        return;
      }

      // For development, we need to import from the compiled JS files
      const compiledPath = path.join(
        process.cwd(),
        "dist",
        "plugins",
        pluginDir,
        "index.js"
      );

      if (!fs.existsSync(compiledPath)) {
        console.log(
          `⚠️  No compiled index.js found for plugin: ${pluginDir}, skipping (run build first)`
        );
        return;
      }

      // Dynamically import the plugin metadata
      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
      const pluginModule = await import(compiledPath);
      // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
      const pluginMetadata = pluginModule.default as CreatePluginDto;

      if (!pluginMetadata || !pluginMetadata.key) {
        console.log(
          `⚠️  No valid plugin metadata found for: ${pluginDir}, skipping`
        );
        return;
      }

      // Check if plugin is already registered
      try {
        const existingPlugin = await this.pluginService.findByKey(
          pluginMetadata.key
        );
        if (existingPlugin) {
          console.log(
            `🔄 Plugin "${pluginMetadata.key}" already registered, updating...`
          );
          await this.pluginService.update(
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            String((existingPlugin as any).id),
            pluginMetadata
          );
          console.log(`✅ Updated plugin: ${pluginMetadata.name}`);
          return;
        }
      } catch {
        // Plugin doesn't exist, continue with registration
      }

      // Register new plugin
      const registeredPlugin =
        await this.pluginService.registerPlugin(pluginMetadata);
      console.log(
        `🎉 Registered new plugin: ${registeredPlugin.name} (${registeredPlugin.key})`
      );
    } catch (error) {
      console.error(`❌ Error registering plugin ${pluginDir}:`, error);
    }
  }
}
