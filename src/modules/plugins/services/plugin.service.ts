import { Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { Plugin } from "../schemas/plugin.schema";
import { PluginServiceInterface } from "../interfaces/plugin-service.interface";
import { CreatePluginDto } from "../dto/create-plugin.dto";
import { I18nHelper } from "../../../common/utils/i18n.helper";
import * as path from "path";

@Injectable()
export class PluginService implements PluginServiceInterface {
  constructor(@InjectModel(Plugin.name) private pluginModel: Model<Plugin>) {}

  async registerPlugin(createPluginDto: CreatePluginDto): Promise<Plugin> {
    try {
      // Check if plugin already exists
      const existingPlugin = await this.pluginModel
        .findOne({ key: createPluginDto.key })
        .exec();

      if (existingPlugin) {
        throw I18nHelper.badRequest("errors.plugins.already_exists");
      }

      // Create new plugin
      const plugin = new this.pluginModel(createPluginDto);
      return await plugin.save();
    } catch (error) {
      if (error?.status) {
        throw error;
      }
      console.error("Error registering plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.registration_failed");
    }
  }

  async findAll(): Promise<Plugin[]> {
    try {
      return await this.pluginModel.find().exec();
    } catch (error) {
      console.error("Error finding plugins:", error);
      throw I18nHelper.badRequest("errors.plugins.find_failed");
    }
  }

  async findById(id: string): Promise<Plugin> {
    try {
      const plugin = await this.pluginModel.findById(id).exec();
      if (!plugin) {
        throw I18nHelper.notFound("errors.plugins.not_found");
      }
      return plugin;
    } catch (error) {
      if (error?.status) {
        throw error;
      }
      console.error("Error finding plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.find_failed");
    }
  }

  async findByKey(key: string): Promise<Plugin> {
    try {
      const plugin = await this.pluginModel.findOne({ key }).exec();
      if (!plugin) {
        throw I18nHelper.notFound("errors.plugins.not_found");
      }
      return plugin;
    } catch (error) {
      if (error?.status) {
        throw error;
      }
      console.error("Error finding plugin by key:", error);
      throw I18nHelper.badRequest("errors.plugins.find_failed");
    }
  }

  async update(
    id: string,
    updatePluginDto: Partial<CreatePluginDto>
  ): Promise<Plugin> {
    try {
      const plugin = await this.pluginModel
        .findByIdAndUpdate(id, updatePluginDto, { new: true })
        .exec();

      if (!plugin) {
        throw I18nHelper.notFound("errors.plugins.not_found");
      }
      return plugin;
    } catch (error) {
      if (error?.status) {
        throw error;
      }
      console.error("Error updating plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.update_failed");
    }
  }

  async remove(id: string): Promise<{ deleted: boolean }> {
    try {
      const plugin = await this.pluginModel.findById(id).exec();

      if (!plugin) {
        throw I18nHelper.notFound("errors.plugins.not_found");
      }

      // Don't allow removing system plugins
      if (plugin.isSystem) {
        throw I18nHelper.badRequest(
          "errors.plugins.cannot_remove_system_plugin"
        );
      }

      await this.pluginModel.findByIdAndDelete(id).exec();
      return { deleted: true };
    } catch (error) {
      if (error?.status) {
        throw error;
      }
      console.error("Error removing plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.remove_failed");
    }
  }

  async loadPlugin(plugin: Plugin): Promise<any> {
    try {
      // In a real implementation, this would dynamically load the plugin code
      // For safety and example purposes, we're just returning the plugin configuration
      console.log(`Loading plugin: ${plugin.name} from ${plugin.entryPoint}`);

      // In a real implementation you would:
      // 1. Resolve the plugin path
      // 2. Import or require the plugin module
      // 3. Initialize the plugin
      // 4. Return the plugin instance

      return {
        name: plugin.name,
        version: plugin.version,
        loaded: true,
        entryPoint: plugin.entryPoint,
        settings: plugin.defaultSettings,
      };
    } catch (error) {
      console.error(`Error loading plugin ${plugin.name}:`, error);
      throw I18nHelper.badRequest("errors.plugins.load_failed");
    }
  }
}
