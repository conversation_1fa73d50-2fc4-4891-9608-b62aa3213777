import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import {
  WorkspacePlugin,
  WorkspacePluginDocument,
  PluginInstallationStatus,
} from "../schemas/workspace-plugin.schema";
import { WorkspacePluginServiceInterface } from "../interfaces/workspace-plugin-service.interface";
import { InstallPluginDto } from "../dto/install-plugin.dto";
import { UpdatePluginSettingsDto } from "../dto/update-plugin-settings.dto";
import { I18nHelper } from "../../../common/utils/i18n.helper";
import { PluginServiceInterface } from "../interfaces/plugin-service.interface";
import { Inject } from "@nestjs/common";
import { WorkspaceServiceInterface } from "../../workspace/interfaces/workspace-service.interface";
import { WorkspaceMemberServiceInterface } from "../../workspace/interfaces/workspace-member-service.interface";
import { WorkspaceMemberRole } from "../../workspace/schemas/workspace-member.schema";
import { Plugin, PluginDocument } from "../schemas/plugin.schema";

@Injectable()
export class WorkspacePluginService implements WorkspacePluginServiceInterface {
  constructor(
    @InjectModel(WorkspacePlugin.name)
    private workspacePluginModel: Model<WorkspacePluginDocument>,
    @Inject("PluginServiceInterface")
    private pluginService: PluginServiceInterface,
    @Inject("WorkspaceServiceInterface")
    private workspaceService: WorkspaceServiceInterface,
    @Inject("WorkspaceMemberServiceInterface")
    private workspaceMemberService: WorkspaceMemberServiceInterface
  ) {}

  /**
   * Verify that a user has access to manage plugins in a workspace
   */
  private async verifyAccess(
    workspaceId: string,
    userId: string,
    requireAdmin = true
  ): Promise<void> {
    try {
      const isMember = await this.workspaceMemberService.isMember(
        workspaceId,
        userId
      );

      if (isMember) {
        throw I18nHelper.forbidden("errors.workspace.insufficient_permissions");
      }
    } catch (error) {
      throw I18nHelper.forbidden("errors.workspace.access_verification_failed");
    }
  }

  async install(
    workspaceId: string,
    userId: string,
    installDto: InstallPluginDto
  ): Promise<WorkspacePlugin> {
    try {
      // Verify user has admin access to the workspace
      await this.verifyAccess(workspaceId, userId);

      // Find the plugin by ID or key
      let plugin;
      try {
        // Try to find by ID first
        plugin = (await this.pluginService.findById(
          installDto.pluginId
        )) as PluginDocument;
      } catch (error) {
        // If not found by ID, try by key
        plugin = (await this.pluginService.findByKey(
          installDto.pluginId
        )) as PluginDocument;
      }

      // Check if plugin is already installed
      const existingInstallation = await this.workspacePluginModel.findOne({
        workspace: workspaceId,
        plugin: plugin._id,
      });

      if (existingInstallation) {
        throw I18nHelper.badRequest("errors.plugins.already_installed");
      }

      // Merge default settings with custom settings
      const settings = {
        ...plugin.defaultSettings,
        ...installDto.settings,
      };

      // Create new installation
      const workspacePlugin = new this.workspacePluginModel({
        workspace: workspaceId,
        plugin: plugin._id,
        status: PluginInstallationStatus.ACTIVE,
        settings,
        installedAt: new Date(),
        lastUpdatedAt: new Date(),
      });

      // Save installation
      return await workspacePlugin.save();
    } catch (error) {
      if (error.status) {
        throw error;
      }
      console.error("Error installing plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.installation_failed");
    }
  }

  async uninstall(
    workspaceId: string,
    userId: string,
    pluginId: string
  ): Promise<{ uninstalled: boolean }> {
    try {
      // Verify user has admin access to the workspace
      await this.verifyAccess(workspaceId, userId);

      // Find the plugin installation
      const installation = await this.findPluginInstallation(
        workspaceId,
        pluginId
      );

      // Find the plugin to check if it's a system plugin
      const plugin = await this.pluginService.findById(
        installation.plugin.toString()
      );

      // Don't allow uninstalling system plugins
      if (plugin.isSystem) {
        throw I18nHelper.badRequest(
          "errors.plugins.cannot_uninstall_system_plugin"
        );
      }

      // Delete the installation
      await this.workspacePluginModel.findByIdAndDelete(installation.id);

      return { uninstalled: true };
    } catch (error) {
      if (error.status) {
        throw error;
      }
      console.error("Error uninstalling plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.uninstallation_failed");
    }
  }

  async enable(
    workspaceId: string,
    userId: string,
    pluginId: string
  ): Promise<WorkspacePlugin> {
    try {
      // Verify user has admin access to the workspace
      await this.verifyAccess(workspaceId, userId);

      // Find the plugin installation
      const installation = await this.findPluginInstallation(
        workspaceId,
        pluginId
      );

      // Update status to active
      installation.status = PluginInstallationStatus.ACTIVE;
      installation.lastUpdatedAt = new Date();

      return await installation.save();
    } catch (error) {
      if (error.status) {
        throw error;
      }
      console.error("Error enabling plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.enabling_failed");
    }
  }

  async disable(
    workspaceId: string,
    userId: string,
    pluginId: string
  ): Promise<WorkspacePlugin> {
    try {
      // Verify user has admin access to the workspace
      await this.verifyAccess(workspaceId, userId);

      // Find the plugin installation
      const installation = await this.findPluginInstallation(
        workspaceId,
        pluginId
      );

      // Find the plugin to check if it's a system plugin
      const plugin = await this.pluginService.findById(
        installation.plugin.toString()
      );

      // Don't allow disabling system plugins
      if (plugin.isSystem) {
        throw I18nHelper.badRequest(
          "errors.plugins.cannot_disable_system_plugin"
        );
      }

      // Update status to disabled
      installation.status = PluginInstallationStatus.DISABLED;
      installation.lastUpdatedAt = new Date();

      return await installation.save();
    } catch (error) {
      if (error.status) {
        throw error;
      }
      console.error("Error disabling plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.disabling_failed");
    }
  }

  async updateSettings(
    workspaceId: string,
    userId: string,
    pluginId: string,
    updateDto: UpdatePluginSettingsDto
  ): Promise<WorkspacePlugin> {
    try {
      // Verify user has admin access to the workspace
      await this.verifyAccess(workspaceId, userId);

      // Find the plugin installation
      const installation = await this.findPluginInstallation(
        workspaceId,
        pluginId
      );

      // Update settings
      if (updateDto.settings) {
        installation.settings = {
          ...installation.settings,
          ...updateDto.settings,
        };
      }

      installation.lastUpdatedAt = new Date();

      // Save changes
      return await installation.save();
    } catch (error) {
      if (error.status) {
        throw error;
      }
      console.error("Error updating plugin settings:", error);
      throw I18nHelper.badRequest("errors.plugins.settings_update_failed");
    }
  }

  async findAllByWorkspace(
    workspaceId: string,
    userId: string
  ): Promise<WorkspacePlugin[]> {
    try {
      // Verify user has access to the workspace (doesn't need to be admin)
      await this.verifyAccess(workspaceId, userId, false);

      // Find all plugins for the workspace
      return await this.workspacePluginModel
        .find({ workspace: workspaceId })
        .populate("plugin")
        .exec();
    } catch (error) {
      if (error.status) {
        throw error;
      }
      console.error("Error finding workspace plugins:", error);
      throw I18nHelper.badRequest("errors.plugins.find_failed");
    }
  }

  async findOne(
    workspaceId: string,
    userId: string,
    pluginId: string
  ): Promise<WorkspacePlugin> {
    try {
      // Verify user has access to the workspace (doesn't need to be admin)
      await this.verifyAccess(workspaceId, userId, false);

      // Find the plugin installation
      const installation = await this.findPluginInstallation(
        workspaceId,
        pluginId
      );

      // Populate plugin details
      await installation.populate("plugin");

      return installation;
    } catch (error) {
      if (error.status) {
        throw error;
      }
      console.error("Error finding workspace plugin:", error);
      throw I18nHelper.badRequest("errors.plugins.find_failed");
    }
  }

  async isPluginEnabled(
    workspaceId: string,
    pluginIdOrKey: string
  ): Promise<boolean> {
    try {
      // First determine if we have a plugin ID or a plugin key
      let plugin;
      let pluginId = pluginIdOrKey;

      // If it doesn't look like an ObjectId, assume it's a key
      if (!pluginIdOrKey.match(/^[0-9a-fA-F]{24}$/)) {
        plugin = (await this.pluginService.findByKey(
          pluginIdOrKey
        )) as PluginDocument;
        pluginId = plugin._id.toString();
      } else {
        // Otherwise, find the plugin by ID
        plugin = (await this.pluginService.findById(
          pluginIdOrKey
        )) as PluginDocument;
      }

      // Find the installation
      const installation = await this.workspacePluginModel.findOne({
        workspace: workspaceId,
        plugin: plugin._id,
      });

      // Return true if installed and active
      return (
        installation !== null &&
        installation.status === PluginInstallationStatus.ACTIVE
      );
    } catch (error) {
      // If plugin not found or any other error, consider it not enabled
      return false;
    }
  }

  /**
   * Execute a plugin action
   */
  async executePluginAction(
    workspaceId: string,
    pluginIdOrKey: string,
    action: string,
    params?: Record<string, any>
  ): Promise<any> {
    try {
      // First check if plugin is enabled
      const isEnabled = await this.isPluginEnabled(workspaceId, pluginIdOrKey);

      if (!isEnabled) {
        throw I18nHelper.badRequest("errors.plugins.not_enabled");
      }

      // Get plugin ID
      let pluginId = pluginIdOrKey;
      let plugin;

      if (!pluginIdOrKey.match(/^[0-9a-fA-F]{24}$/)) {
        plugin = (await this.pluginService.findByKey(
          pluginIdOrKey
        )) as PluginDocument;
        pluginId = plugin._id.toString();
      } else {
        plugin = (await this.pluginService.findById(
          pluginId
        )) as PluginDocument;
      }

      // Find installation to get settings
      const installation = await this.findPluginInstallation(
        workspaceId,
        pluginId
      );

      // Load the plugin
      const pluginInstance = await this.pluginService.loadPlugin(plugin);

      // In a real implementation, you would call a method on the pluginInstance
      // For this example, we'll simulate the action
      console.log(
        `Executing action "${action}" on plugin "${plugin.name}" with params:`,
        params
      );

      // Update usage statistics
      await this.updateUsageStats(installation, action);

      // Return mock result
      return {
        success: true,
        plugin: plugin.name,
        action,
        result: `Action "${action}" executed successfully on ${plugin.name}`,
        timestamp: new Date(),
      };
    } catch (error) {
      if (error.status) {
        throw error;
      }
      console.error("Error executing plugin action:", error);
      throw I18nHelper.badRequest("errors.plugins.action_execution_failed");
    }
  }

  /**
   * Helper method to find a plugin installation
   */
  private async findPluginInstallation(
    workspaceId: string,
    pluginId: string
  ): Promise<WorkspacePluginDocument> {
    // Try to find by plugin ID
    let installation = await this.workspacePluginModel.findOne({
      workspace: workspaceId,
      plugin: pluginId,
    });

    // If not found, try to find by plugin key
    if (!installation && !pluginId.match(/^[0-9a-fA-F]{24}$/)) {
      // Find plugin by key first to get its ID
      const plugin = (await this.pluginService.findByKey(
        pluginId
      )) as PluginDocument;

      // Then find installation by plugin ID
      installation = await this.workspacePluginModel.findOne({
        workspace: workspaceId,
        plugin: plugin._id,
      });
    }

    if (!installation) {
      throw I18nHelper.notFound("errors.plugins.installation_not_found");
    }

    return installation;
  }

  /**
   * Update usage statistics for a plugin
   */
  private async updateUsageStats(
    installation: WorkspacePluginDocument,
    action: string
  ): Promise<void> {
    // Initialize usage object if not present
    if (!installation.usage) {
      installation.usage = {};
    }

    // Update total executions
    installation.usage.totalExecutions =
      (installation.usage.totalExecutions || 0) + 1;

    // Update last executed timestamp
    installation.usage.lastExecutedAt = new Date();

    // Keep track of actions executed
    if (!installation.usage.actions) {
      installation.usage.actions = {};
    }

    // Increment count for this specific action
    installation.usage.actions[action] =
      (installation.usage.actions[action] || 0) + 1;

    // Save changes
    installation.markModified("usage");
    await installation.save();
  }
}
