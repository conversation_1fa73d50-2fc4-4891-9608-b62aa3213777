import { WorkspacePlugin } from "../schemas/workspace-plugin.schema";
import { InstallPluginDto } from "../dto/install-plugin.dto";
import { UpdatePluginSettingsDto } from "../dto/update-plugin-settings.dto";

export interface WorkspacePluginServiceInterface {
  /**
   * Install a plugin in a workspace
   * @param workspaceId The workspace ID
   * @param userId The user ID performing the action
   * @param installDto Installation data
   */
  install(
    workspaceId: string,
    userId: string,
    installDto: InstallPluginDto
  ): Promise<WorkspacePlugin>;

  /**
   * Uninstall a plugin from a workspace
   * @param workspaceId The workspace ID
   * @param userId The user ID performing the action
   * @param pluginId The plugin ID to uninstall
   */
  uninstall(
    workspaceId: string,
    userId: string,
    pluginId: string
  ): Promise<{ uninstalled: boolean }>;

  /**
   * Enable a previously disabled plugin
   * @param workspaceId The workspace ID
   * @param userId The user ID performing the action
   * @param pluginId The plugin ID to enable
   */
  enable(
    workspaceId: string,
    userId: string,
    pluginId: string
  ): Promise<WorkspacePlugin>;

  /**
   * Disable a plugin temporarily
   * @param workspaceId The workspace ID
   * @param userId The user ID performing the action
   * @param pluginId The plugin ID to disable
   */
  disable(
    workspaceId: string,
    userId: string,
    pluginId: string
  ): Promise<WorkspacePlugin>;

  /**
   * Update a plugin's settings
   * @param workspaceId The workspace ID
   * @param userId The user ID performing the action
   * @param pluginId The plugin ID to update
   * @param updateDto Updated settings
   */
  updateSettings(
    workspaceId: string,
    userId: string,
    pluginId: string,
    updateDto: UpdatePluginSettingsDto
  ): Promise<WorkspacePlugin>;

  /**
   * Find all plugins installed in a workspace
   * @param workspaceId The workspace ID
   * @param userId The user ID performing the action
   */
  findAllByWorkspace(
    workspaceId: string,
    userId: string
  ): Promise<WorkspacePlugin[]>;

  /**
   * Get details of a specific plugin installation
   * @param workspaceId The workspace ID
   * @param userId The user ID performing the action
   * @param pluginId The plugin ID
   */
  findOne(
    workspaceId: string,
    userId: string,
    pluginId: string
  ): Promise<WorkspacePlugin>;

  /**
   * Check if a plugin is installed and active in a workspace
   * @param workspaceId The workspace ID
   * @param pluginIdOrKey The plugin ID or key
   */
  isPluginEnabled(workspaceId: string, pluginIdOrKey: string): Promise<boolean>;

  /**
   * Execute a plugin action
   * @param workspaceId The workspace ID
   * @param pluginIdOrKey The plugin ID or key
   * @param action The action to execute
   * @param params Action parameters
   */
  executePluginAction(
    workspaceId: string,
    pluginIdOrKey: string,
    action: string,
    params?: Record<string, any>
  ): Promise<any>;
}
