import { Plugin } from "../schemas/plugin.schema";
import { CreatePluginDto } from "../dto/create-plugin.dto";

export interface PluginServiceInterface {
  /**
   * Register a new plugin in the system
   * @param createPluginDto The plugin data
   */
  registerPlugin(createPluginDto: CreatePluginDto): Promise<Plugin>;

  /**
   * Find all available plugins
   */
  findAll(): Promise<Plugin[]>;

  /**
   * Find a plugin by its ID
   * @param id Plugin ID
   */
  findById(id: string): Promise<Plugin>;

  /**
   * Find a plugin by its unique key
   * @param key Plugin key
   */
  findByKey(key: string): Promise<Plugin>;

  /**
   * Update a plugin
   * @param id Plugin ID
   * @param updatePluginDto Updated plugin data
   */
  update(
    id: string,
    updatePluginDto: Partial<CreatePluginDto>
  ): Promise<Plugin>;

  /**
   * Remove a plugin from the system
   * @param id Plugin ID
   */
  remove(id: string): Promise<{ deleted: boolean }>;

  /**
   * Load a plugin by executing its entry point
   * @param plugin The plugin to load
   */
  loadPlugin(plugin: Plugin): Promise<any>;
}
