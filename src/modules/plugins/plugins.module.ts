import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { Plugin, PluginSchema } from "./schemas/plugin.schema";
import {
  WorkspacePlugin,
  WorkspacePluginSchema,
} from "./schemas/workspace-plugin.schema";
import { PluginService } from "./services/plugin.service";
import { WorkspacePluginService } from "./services/workspace-plugin.service";
import { PluginAutoRegistrationService } from "./services/plugin-auto-registration.service";
import { PluginController } from "./controllers/plugin.controller";
import { WorkspacePluginController } from "./controllers/workspace-plugin.controller";
import { WorkspaceModule } from "../workspace/workspace.module";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: Plugin.name, schema: PluginSchema },
      { name: WorkspacePlugin.name, schema: WorkspacePluginSchema },
    ]),
    WorkspaceModule,
  ],
  controllers: [PluginController, WorkspacePluginController],
  providers: [
    {
      provide: "PluginServiceInterface",
      useClass: PluginService,
    },
    {
      provide: "WorkspacePluginServiceInterface",
      useClass: WorkspacePluginService,
    },
    PluginAutoRegistrationService, // Auto-register plugins on startup
  ],
  exports: ["PluginServiceInterface", "WorkspacePluginServiceInterface"],
})
export class PluginsModule {}
