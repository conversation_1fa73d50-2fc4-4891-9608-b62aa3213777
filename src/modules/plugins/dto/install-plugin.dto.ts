import { ApiProperty } from "@nestjs/swagger";
import { IsNotEmpty, IsObject, IsOptional, IsString } from "class-validator";

export class InstallPluginDto {
  @ApiProperty({
    description: "Plugin ID or key to install",
    example: "shopify-integration",
  })
  @IsString()
  @IsNotEmpty()
  pluginId: string;

  @ApiProperty({
    description: "Custom settings for this plugin installation",
    example: {
      apiKey: "sk_123456",
      shopDomain: "mystore.shopify.com",
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  settings?: Record<string, any>;
}
