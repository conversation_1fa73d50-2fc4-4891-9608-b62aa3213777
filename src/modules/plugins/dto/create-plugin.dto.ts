import { ApiProperty } from "@nestjs/swagger";
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsUrl,
  IsBoolean,
  IsArray,
  IsObject,
} from "class-validator";
import { PluginCategory } from "../schemas/plugin.schema";

export class CreatePluginDto {
  @ApiProperty({
    description: "Plugin unique identifier",
    example: "shopify-integration",
  })
  @IsString()
  @IsNotEmpty()
  key: string;

  @ApiProperty({
    description: "Plugin name",
    example: "Shopify Integration",
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: "Plugin description",
    example: "Integrates your chat with Shopify store data and events",
  })
  @IsString()
  @IsNotEmpty()
  description: string;

  @ApiProperty({
    description: "Plugin version",
    example: "1.0.0",
  })
  @IsString()
  @IsNotEmpty()
  version: string;

  @ApiProperty({
    description: "Plugin author",
    example: "OnlyChat Team",
  })
  @IsString()
  @IsNotEmpty()
  author: string;

  @ApiProperty({
    description: "Plugin category",
    enum: PluginCategory,
    example: PluginCategory.INTEGRATION,
  })
  @IsEnum(PluginCategory)
  @IsNotEmpty()
  category: PluginCategory;

  @ApiProperty({
    description: "Plugin icon URL",
    example: "https://storage.cloud.com/plugins/shopify-icon.png",
    required: false,
  })
  @IsUrl()
  @IsOptional()
  iconUrl?: string;

  @ApiProperty({
    description: "Link to plugin documentation",
    example: "https://docs.onlychat.com/plugins/shopify",
    required: false,
  })
  @IsUrl()
  @IsOptional()
  documentationUrl?: string;

  @ApiProperty({
    description: "Main entry point for the plugin (relative path)",
    example: "src/plugins/shopify/index.js",
  })
  @IsString()
  @IsNotEmpty()
  entryPoint: string;

  @ApiProperty({
    description: "Whether the plugin is globally available for all workspaces",
    example: true,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isGloballyAvailable?: boolean;

  @ApiProperty({
    description: "Whether the plugin is system plugin (cannot be uninstalled)",
    example: false,
    required: false,
  })
  @IsBoolean()
  @IsOptional()
  isSystem?: boolean;

  @ApiProperty({
    description: "Plugin settings schema (JSON Schema format)",
    example: {
      type: "object",
      properties: {
        apiKey: { type: "string" },
        shopDomain: { type: "string" },
      },
      required: ["apiKey", "shopDomain"],
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  settingsSchema?: Record<string, any>;

  @ApiProperty({
    description: "Default plugin settings",
    example: {
      webhookEnabled: true,
    },
    required: false,
  })
  @IsObject()
  @IsOptional()
  defaultSettings?: Record<string, any>;

  @ApiProperty({
    description: "Required permissions for this plugin",
    example: ["chat:read", "chat:write", "contacts:read"],
    required: false,
  })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  requiredPermissions?: string[];
}
