import { forwardRef, Module } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import { UsersController } from "./controllers/users.controller";
import { UsersService } from "./services/users.service";
import { User, UserSchema } from "./schemas/user.schema";
import { UserResolver } from "./graphql/resolvers/user.resolver";
import { UserFieldResolver } from "./graphql/resolvers/user-field.resolver";
import { AuthModule } from "../auth";
import { RedisModule } from "src/services/redis/redis.module";

@Module({
  imports: [
    MongooseModule.forFeature([{ name: User.name, schema: UserSchema }]),
    forwardRef(() => AuthModule),
    RedisModule,
  ],
  controllers: [UsersController],
  providers: [
    {
      provide: "UserServiceInterface",
      useClass: UsersService,
    },
    UserResolver,
    UserFieldResolver,
  ],
  exports: ["UserServiceInterface", UserResolver, UserFieldResolver],
})
export class UsersModule {}
