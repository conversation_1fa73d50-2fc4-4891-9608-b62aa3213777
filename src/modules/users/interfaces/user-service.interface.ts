import { UserDocument } from "../entities/user.entity";
import { UpdateUserInfoDto } from "../dto/update-user-profile.dto";

export interface UserServiceInterface {
  findOne(id: string): Promise<UserDocument | null>;
  findByEmail(email: string): Promise<UserDocument | null>;
  findManyByIds(ids: string[]): Promise<UserDocument[]>;
  updateOne(id: string, body: UpdateUserInfoDto): Promise<UserDocument>;

  setCurrentWorkspace(
    id: string,
    workspaceId: string
  ): Promise<{ message: string; token: string; userId: string }>;

  createTempUser(email: string): Promise<UserDocument>;

  updatePassword(id: string, password: string): Promise<UserDocument>;

  createUser(
    firstName: string,
    lastName: string,
    email: string,
    password: string
  ): Promise<UserDocument>;

  createGoogleUser(
    firstName: string,
    lastName: string,
    email: string,
    googleId: string,
    avatar?: string
  ): Promise<UserDocument>;
}
