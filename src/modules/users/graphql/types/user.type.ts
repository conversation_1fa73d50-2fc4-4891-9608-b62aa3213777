import { Field, ID, ObjectType } from "@nestjs/graphql";
import { Node } from "src/modules/graphql/types/node.interface";
import { UserRole, UserStatus } from "../../schemas/user.schema";

@ObjectType("User", { implements: [Node] })
export class UserType implements Node {
  @Field(() => ID)
  id: string;

  @Field(() => String, { nullable: true })
  firstName?: string;

  @Field(() => String, { nullable: true })
  lastName?: string;

  @Field(() => String)
  email: string;

  @Field(() => String, { nullable: true })
  phone?: string;

  @Field(() => String, { nullable: true })
  avatar?: string;

  @Field(() => String)
  role: UserRole;

  @Field(() => String)
  status: UserStatus;

  @Field(() => Boolean, { defaultValue: false })
  isOnline: boolean;

  @Field(() => Date, { nullable: true })
  lastActivityAt?: Date;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;

  // Hidden field to store workspace context for isOnline field resolution
  // This field is not exposed in GraphQL schema but used internally
  workspaceId?: string;
}
