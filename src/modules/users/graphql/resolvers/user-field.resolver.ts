import { <PERSON><PERSON><PERSON>, ResolveField, Parent } from "@nestjs/graphql";
import { UserType } from "../types/user.type";
import { UserStatusRedisService } from "src/services/redis/user-status.redis.service";
import { User } from "@app/users/entities/user.entity";
import { toGlobalId } from "graphql-relay";

@Resolver(() => UserType)
export class UserFieldResolver {
  constructor(
    private readonly userStatusRedisService: UserStatusRedisService
  ) {}

  @ResolveField(() => String)
  id(@Parent() user: User) {
    return toGlobalId("User", user.id);
  }

  @ResolveField(() => String)
  rawId(@Parent() user: User) {
    return user.id;
  }

  @ResolveField(() => Boolean, { defaultValue: false })
  async isOnline(@Parent() user: UserType): Promise<boolean> {
    // If no workspace ID is available, default to false
    if (!user.workspaceId) {
      console.log("No workspace ID available for user", user.id);
      return false;
    }
    try {
      // Check online status from Redis only when isOnline field is requested
      const onlineStatus = await this.userStatusRedisService.isUserOnline(
        user.id,
        user.workspaceId
      );

      return onlineStatus === "online";
    } catch (error) {
      // If Redis is unavailable, default to false
      console.error("Failed to check user online status:", error);
      return false;
    }
  }
}
