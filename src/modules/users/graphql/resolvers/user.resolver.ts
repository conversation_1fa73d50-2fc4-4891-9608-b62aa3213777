import { Resolver, Query } from "@nestjs/graphql";
import { Inject, UseGuards } from "@nestjs/common";

import { UserType } from "../types/user.type";
import { UserServiceInterface } from "@app/users/interfaces/user-service.interface";
import { GqlAuthGuard } from "src/modules/auth/guards/gql-auth.guard";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { User } from "@app/users/entities/user.entity";
import { JwtPayload } from "src/modules/auth/interfaces/jwt-payload.interface";

@Resolver(() => UserType)
export class UserResolver {
  constructor(
    @Inject("UserServiceInterface")
    private readonly userService: UserServiceInterface
  ) {}

  @Query(() => UserType, { nullable: true })
  @UseGuards(GqlAuthGuard)
  async me(@CurrentUser() user: JwtPayload): Promise<UserType | null> {
    const userId = user.sub;
    const userInfo = await this.userService.findOne(userId);
    if (!userInfo) {
      return null;
    }
    const userType = new User(userInfo).toGraphQL();
    userType.workspaceId = user.workspaceId;
    return userType;
  }
}
