import { Prop, Schema, SchemaFactory } from "@nestjs/mongoose";

export enum UserRole {
  USER = "user",
  ADMIN = "admin",
}

export enum UserStatus {
  PENDING = "pending",
  ACTIVE = "active",
  BLOCKED = "blocked",
}

@Schema({ timestamps: true })
export class User {
  @Prop({ required: false })
  firstName?: string;

  @Prop({ required: false })
  lastName?: string;

  @Prop({ required: true, unique: true })
  email: string;

  @Prop({ required: false })
  password?: string;

  @Prop({ required: false })
  phone?: string;

  @Prop({ required: false })
  avatar?: string;

  @Prop({ required: false })
  googleId?: string;

  @Prop({
    type: String,
    enum: Object.values(UserRole),
    default: UserRole.USER,
  })
  role: UserRole;

  @Prop({
    type: String,
    enum: Object.values(UserStatus),
    default: UserStatus.PENDING,
  })
  status: UserStatus;
}

export const UserSchema = SchemaFactory.createForClass(User);
