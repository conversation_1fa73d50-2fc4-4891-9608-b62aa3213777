import { Inject, Injectable, NotFoundException } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model, Types } from "mongoose";
import { User, UserDocument } from "../entities/user.entity";
import { UserServiceInterface } from "../interfaces/user-service.interface";
import { I18nHelper } from "../../../common/utils/i18n.helper";
import { UpdateUserInfoDto } from "../dto/update-user-profile.dto";
import { AuthServiceInterface } from "src/modules/auth";
import { UserStatusRedisService } from "src/services/redis/user-status.redis.service";
import { UserStatus } from "../schemas/user.schema";

@Injectable()
export class UsersService implements UserServiceInterface {
  constructor(
    @InjectModel(User.name) private userModel: Model<User>,
    @Inject("AuthServiceInterface")
    private readonly authService: AuthServiceInterface,
    private readonly userStatusRedisService: UserStatusRedisService
  ) {}

  async findOne(id: string): Promise<UserDocument> {
    const user = await this.userModel.findById(id).exec();
    if (!user) {
      throw I18nHelper.notFound("errors.users.not_found");
    }
    return user;
  }

  async findByEmail(email: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ email }).exec();
  }

  async findManyByIds(ids: string[]): Promise<UserDocument[]> {
    console.log({ ids });
    return this.userModel
      .find({ _id: { $in: ids.map((id) => new Types.ObjectId(id)) } })
      .exec();
  }

  async updateOne(id: string, body: UpdateUserInfoDto): Promise<UserDocument> {
    const updatedUser = await this.userModel
      .findByIdAndUpdate(
        id,
        {
          $set: body,
        },
        { new: true }
      )
      .exec();

    if (!updatedUser) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return updatedUser;
  }

  setCurrentWorkspace(
    id: string,
    workspaceId: string
  ): Promise<{ message: string; token: string; userId: string }> {
    return this.authService.loginWithWorkspace(id, workspaceId);
  }

  async createTempUser(email: string): Promise<UserDocument> {
    const user = await this.userModel.create({
      email,
      status: UserStatus.PENDING,
    });
    return user;
  }

  async updatePassword(id: string, password: string): Promise<UserDocument> {
    const updatedUser = await this.userModel
      .findByIdAndUpdate(
        id,
        {
          $set: { password },
        },
        { new: true }
      )
      .exec();

    if (!updatedUser) {
      throw new NotFoundException(`User with ID ${id} not found`);
    }

    return updatedUser;
  }

  createUser(
    firstName: string,
    lastName: string,
    email: string,
    password: string
  ): Promise<UserDocument> {
    return this.userModel.create({
      email,
      password,
      status: UserStatus.ACTIVE,
      firstName,
      lastName,
    });
  }

  createGoogleUser(
    firstName: string,
    lastName: string,
    email: string,
    googleId: string,
    avatar?: string
  ): Promise<UserDocument> {
    return this.userModel.create({
      email,
      status: UserStatus.ACTIVE,
      firstName,
      lastName,
      googleId,
      avatar,
    });
  }
}
