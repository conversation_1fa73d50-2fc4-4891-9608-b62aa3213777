import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsUrl, IsEnum } from "class-validator";
import { UserStatus } from "../schemas/user.schema";

export class UpdateUserProfileDto {
  @ApiProperty({ description: "User's first name", required: false })
  @IsString()
  @IsOptional()
  firstName?: string;

  @ApiProperty({ description: "User's last name", required: false })
  @IsString()
  @IsOptional()
  lastName?: string;

  @ApiProperty({ description: "User's phone number", required: false })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ description: "User's avatar URL", required: false })
  @IsUrl()
  @IsOptional()
  avatar?: string;
}

export class UpdateUserInfoDto extends UpdateUserProfileDto {
  @IsEnum(UserStatus)
  @IsOptional()
  status?: UserStatus;

  @IsString()
  @IsOptional()
  password?: string;

  @IsString()
  @IsOptional()
  googleId?: string;
}
