import { UserRole, UserStatus } from "../schemas/user.schema";
import { Document, Types } from "mongoose";
import { UserType } from "../graphql/types/user.type";
import {
  BaseDocument,
  BaseEntity,
} from "src/modules/common/entities/base.entity";

abstract class BaseUser extends BaseEntity {
  firstName?: string;
  lastName?: string;
  email!: string;
  phone?: string;
  avatar?: string;
  googleId?: string;
  role!: UserRole;
  status!: UserStatus;
}

export interface UserDocument extends BaseDocument<BaseUser> {
  _id: Types.ObjectId;
  password?: string;
}

export class User extends BaseUser {
  constructor(userDoc?: UserDocument) {
    super();
    if (userDoc) {
      super.assignFields(userDoc);

      this.firstName = userDoc.firstName;
      this.lastName = userDoc.lastName;
      this.email = userDoc.email;
      this.phone = userDoc.phone;
      this.avatar = userDoc.avatar;
      this.role = userDoc.role;
      this.status = userDoc.status;
    }
  }

  toGraphQL(): UserType {
    const userType = new UserType();
    userType.id = this.id;
    userType.firstName = this.firstName;
    userType.lastName = this.lastName;
    userType.email = this.email;
    userType.phone = this.phone;
    userType.avatar = this.avatar;
    userType.role = this.role;
    userType.status = this.status;
    userType.createdAt = this.createdAt;
    userType.updatedAt = this.updatedAt;
    return userType;
  }
}
