import {
  Controller,
  Inject,
  Patch,
  Body,
  UseGuards,
  Request,
} from "@nestjs/common";
import { UserServiceInterface } from "../interfaces/user-service.interface";
import { ApiOperation, ApiTags, ApiBearerAuth } from "@nestjs/swagger";
import { JwtAuthGuard } from "../../auth/guards/jwt-auth.guard";
import { UpdateUserProfileDto } from "../dto/update-user-profile.dto";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { CurrentWorkspaceDto } from "../dto/current-workspace.dto";
import { User } from "../entities/user.entity";

@ApiTags("Users")
@Controller("users")
export class UsersController {
  constructor(
    @Inject("UserServiceInterface")
    private readonly usersService: UserServiceInterface
  ) {}

  @Patch("profile")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Update user profile" })
  async updateProfile(
    @Request() req: RequestWithUser,
    @Body() updateProfileDto: UpdateUserProfileDto
  ): Promise<User> {
    try {
      const user = await this.usersService.updateOne(
        req.user.sub,
        updateProfileDto
      );
      return new User(user);
    } catch (error) {
      throw new Error(error);
    }
  }

  @Patch("current-workspace")
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: "Set current user workspace" })
  async setCurrentWorkspace(
    @Request() req: RequestWithUser,
    @Body() body: CurrentWorkspaceDto
  ): Promise<{ message: string; token: string; userId: string }> {
    return this.usersService.setCurrentWorkspace(
      req.user.sub,
      body.workspaceId
    );
  }
}
