/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Field, Int, ObjectType } from "@nestjs/graphql";
import { EdgeType, PageInfo } from "./connection.type";

export interface BasePagination {
  edges: any;
  pageInfo: PageInfo;
  totalCount: number;
}

export function PaginationType(TItemClass: any): any {
  const EdgeCls = EdgeType(TItemClass);

  @ObjectType(`${TItemClass.name}Pagination`)
  class Pagination implements BasePagination {
    @Field(() => [EdgeCls])
    edges: Array<typeof EdgeCls>;

    @Field(() => PageInfo)
    pageInfo: PageInfo;

    @Field(() => Int)
    totalCount: number;
  }

  return Pagination;
}
