/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Field, Int, ObjectType } from "@nestjs/graphql";
import { PageInfo } from "./connection.type";

export interface BasePagination {
  edges: any;
  pageInfo: PageInfo;
  totalCount: number;
}

export function PaginationEdgeType(TItemClass: any): any {
  // Use "PaginationEdge" suffix to avoid conflicts with ConnectionEdge
  const baseName = (TItemClass.name as string).replace(/Type$/, "");
  @ObjectType(`${baseName}PaginationEdge`)
  class PaginationEdge {
    @Field(() => String)
    cursor: string;

    @Field(() => TItemClass)
    node: typeof TItemClass;
  }
  return PaginationEdge;
}

export function PaginationType(TItemClass: any): any {
  const EdgeCls = PaginationEdgeType(TItemClass);
  // Remove "Type" suffix if it exists to avoid "ContactTypePagination"
  const baseName = (TItemClass.name as string).replace(/Type$/, "");

  @ObjectType(`${baseName}Pagination`)
  class Pagination implements BasePagination {
    @Field(() => [EdgeCls])
    edges: Array<typeof EdgeCls>;

    @Field(() => PageInfo)
    pageInfo: PageInfo;

    @Field(() => Int)
    totalCount: number;
  }

  return Pagination;
}
