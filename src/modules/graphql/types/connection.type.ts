/* eslint-disable @typescript-eslint/no-unsafe-return */
/* eslint-disable @typescript-eslint/no-unsafe-assignment */
import { Field, ObjectType } from "@nestjs/graphql";

@ObjectType()
export class PageInfo {
  @Field()
  hasNextPage: boolean;

  @Field()
  hasPreviousPage: boolean;

  @Field({ nullable: true })
  startCursor?: string;

  @Field({ nullable: true })
  endCursor?: string;
}

export interface BaseConnection {
  edges: any;
  pageInfo: PageInfo;
}

export function EdgeType(TItemClass: any): any {
  @ObjectType(`${TItemClass.name}Edge`)
  class Edge {
    @Field(() => String)
    cursor: string;

    @Field(() => TItemClass)
    node: typeof TItemClass;
  }
  return Edge;
}

export function ConnectionType(TItemClass: any): any {
  const EdgeCls = EdgeType(TItemClass);

  @ObjectType(`${TItemClass.name}Connection`)
  class Connection implements BaseConnection {
    @Field(() => [EdgeCls])
    edges: Array<typeof EdgeCls>;

    @Field(() => PageInfo)
    pageInfo: PageInfo;
  }

  return Connection;
}
