import * as DataLoader from "dataloader";
import { Inject, Injectable, Scope } from "@nestjs/common";
import { Contact, ContactServiceInterface } from "src/modules/contact";

@Injectable({ scope: Scope.REQUEST }) // every request has 1 instance
export class ContactLoader {
  constructor(
    @Inject("ContactServiceInterface")
    private readonly contactService: ContactServiceInterface
  ) {}

  public readonly batchContacts = new DataLoader<string, any>(async (ids) => {
    const items = await this.contactService.findContactsByIds(ids as string[]);
    const contactMap = new Map(
      items.map((itemDoc) => [
        itemDoc._id.toString(),
        new Contact(itemDoc).toGraphQL(),
      ])
    );
    return ids.map((id) => contactMap.get(id));
  });
}
