import * as DataLoader from "dataloader";
import { Inject, Injectable, Scope } from "@nestjs/common";
import { UserServiceInterface } from "@app/users/interfaces/user-service.interface";
import { User } from "@app/users/entities/user.entity";
import { UserType } from "@app/users/graphql/types/user.type";

interface UserWithWorkspace {
  userId: string;
  workspaceId: string;
}

@Injectable({ scope: Scope.REQUEST }) // every request has 1 instance
export class UserLoader {
  constructor(
    @Inject("UserServiceInterface")
    private readonly userService: UserServiceInterface
  ) {}

  public readonly batchUsers = new DataLoader<
    UserWithWorkspace,
    UserType | null
  >(async (items: UserWithWorkspace[]) => {
    // Extract unique user IDs
    const userIds = [...new Set(items.map((key) => key.userId))];

    // Batch load users
    const users = await this.userService.findManyByIds(userIds);
    const userMap = new Map(
      users.map((user) => [user._id.toString(), new User(user)])
    );

    // For each key, get the user (without checking online status)
    const results = items.map((key) => {
      const user = userMap.get(key.userId);
      if (!user) {
        return null;
      }

      // Convert to GraphQL type (isOnline will be resolved separately if requested)
      const userGraphQL = user.toGraphQL();
      // Store workspace context for potential isOnline field resolution
      userGraphQL.workspaceId = key.workspaceId;

      return userGraphQL;
    });

    return results;
  });

  async loadUserWithWorkspace(
    data: UserWithWorkspace
  ): Promise<UserType | null> {
    return this.batchUsers.load(data);
  }
}
