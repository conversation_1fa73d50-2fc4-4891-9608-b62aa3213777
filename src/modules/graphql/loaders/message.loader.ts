import * as DataLoader from "dataloader";
import { Inject, Injectable, Scope } from "@nestjs/common";
import { ChatServiceInterface } from "src/modules/chat/interfaces/chat-service.interface";
import { Message } from "src/modules/chat/entities/message.entity";

@Injectable({ scope: Scope.REQUEST }) // every request has 1 instance
export class MessageLoader {
  constructor(
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface
  ) {}

  public readonly batchMessages = new DataLoader<string, any>(async (ids) => {
    const messages = await this.chatService.findMessagesByIds(ids as string[]);
    const messageMap = new Map(
      messages.map((msgDoc) => [
        msgDoc._id.toString(),
        new Message(msgDoc).toGraphQL(),
      ])
    );
    return ids.map((id) => messageMap.get(id));
  });
}
