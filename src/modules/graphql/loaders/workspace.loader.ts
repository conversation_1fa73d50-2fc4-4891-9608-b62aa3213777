import * as DataLoader from "dataloader";
import { Inject, Injectable, Scope } from "@nestjs/common";
import { WorkspaceServiceInterface } from "src/modules/workspace/interfaces/workspace-service.interface";
import { Workspace } from "src/modules/workspace/entities/workspace.entity";

@Injectable({ scope: Scope.REQUEST }) // every request has 1 instance
export class WorkspaceLoader {
  constructor(
    @Inject("WorkspaceServiceInterface")
    private readonly workspaceService: WorkspaceServiceInterface
  ) {}

  public readonly batchWorkspaces = new DataLoader<string, any>(async (ids) => {
    const items = await this.workspaceService.findWorkspacesByIds(
      ids as string[]
    );
    const workspaceMap = new Map(
      items.map((item) => [
        item._id.toString(),
        new Workspace(item).toGraphQL(),
      ])
    );
    return ids.map((id) => workspaceMap.get(id));
  });
}
