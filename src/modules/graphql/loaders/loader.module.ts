import { forwardRef, Module } from "@nestjs/common";
import { UserLoader } from "./user.loader";
import { UsersModule } from "@app/users/users.module";
import { MessageLoader } from "./message.loader";
import { ChatModule } from "src/modules/chat/chat.module";
import { WorkspaceModule } from "src/modules/workspace/workspace.module";
import { WorkspaceLoader } from "./workspace.loader";
import { ContactLoader } from "./contact.loader";
import { ContactModule } from "src/modules/contact";
import { RedisModule } from "src/services/redis/redis.module";

@Module({
  providers: [UserLoader, MessageLoader, WorkspaceLoader, ContactLoader],
  exports: [UserLoader, MessageLoader, WorkspaceLoader, ContactLoader],
  imports: [
    forwardRef(() => UsersModule),
    forwardRef(() => ChatModule),
    forwardRef(() => WorkspaceModule),
    forwardRef(() => ContactModule),
    RedisModule,
  ],
})
export class LoaderModule {}
