import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { GraphQLModule } from "@nestjs/graphql";
import { ApolloDriver, ApolloDriverConfig } from "@nestjs/apollo";
import { join } from "path";
import { ChatModule } from "../chat/chat.module";
import { WorkspaceModule } from "../workspace/workspace.module";
import { ConfigService } from "@nestjs/config";
import { createGraphQLLoggingPlugin } from "../../common/graphql/graphql-logging.plugin";
import { ApiLoggerService } from "../../common/services/api-logger.service";
import { UsersModule } from "@app/users";
import { ContactModule } from "../contact";
import { NodeResolver } from "./resolvers/node.resolver";

@Module({
  imports: [
    GraphQLModule.forRootAsync<ApolloDriverConfig>({
      driver: ApolloDriver,
      inject: [ConfigService, ApiLoggerService],
      useFactory: (
        configService: ConfigService,
        apiLoggerService: ApiLoggerService
      ) => ({
        graphiql: true,
        introspection: true, // Bật introspection cho cả production
        autoSchemaFile: join(process.cwd(), "src/schema.gql"),
        context: ({ req }) => ({ req }), //to Nest can inject per-request, shouldn't inject UserLoader in useFactory, because it's using Scope.REQUEST
        cors: {
          origin: true, // Allow all origins
          credentials: true, // Allow credentials (cookies, authorization headers, etc)
        },
        plugins: [createGraphQLLoggingPlugin(configService, apiLoggerService)],
      }),
    }),
    ChatModule,
    WorkspaceModule,
    UsersModule,
    ContactModule,
  ],
  providers: [NodeResolver],
})
export class GraphQLAppModule {}
