import { User } from "@app/users/entities/user.entity";
import { UserServiceInterface } from "@app/users/interfaces/user-service.interface";
import { Inject, UseGuards } from "@nestjs/common";
import { Resolver, Query, Args, ID } from "@nestjs/graphql";
import { fromGlobalId } from "graphql-relay";
import { CurrentUser } from "src/modules/auth/decorators/current-user.decorator";
import { GqlAuthGuard } from "src/modules/auth/guards/gql-auth.guard";
import { JwtPayload } from "src/modules/auth/interfaces/jwt-payload.interface";
import { Conversation } from "src/modules/chat/entities/conversation.entity";
import { ChatServiceInterface } from "src/modules/chat/interfaces/chat-service.interface";
import { Contact, ContactServiceInterface } from "src/modules/contact";
import { Node } from "src/modules/graphql/types/node.interface";

@Resolver(() => Node)
export class NodeResolver {
  constructor(
    @Inject("UserServiceInterface")
    private readonly userService: UserServiceInterface,
    @Inject("ChatServiceInterface")
    private readonly chatService: ChatServiceInterface,
    @Inject("ContactServiceInterface")
    private readonly contactService: ContactServiceInterface
  ) {}

  @Query(() => Node, { name: "node", nullable: true })
  @UseGuards(GqlAuthGuard)
  async getNode(
    @Args("id", { type: () => ID }) id: string
  ): Promise<Node | null> {
    const { type, id: realId } = fromGlobalId(id);
    switch (type) {
      case "Contact": {
        const doc = await this.contactService.getContact(realId);
        if (!doc) {
          return null;
        }
        return new Contact(doc).toGraphQL();
      }
      case "User": {
        const doc = await this.userService.findOne(realId);
        if (!doc) {
          return null;
        }
        return new User(doc).toGraphQL();
      }
      case "Conversation": {
        const conv = await this.chatService.getConversationById(realId);
        return new Conversation(conv).toGraphQL();
      }
      default:
        return null;
    }
  }
}
