import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule as NestConfigModule } from "@nestjs/config";
import * as Jo<PERSON> from "joi";
import { appConfig } from "./app.config";
import { mailConfig } from "./mail.config";
import { spaceConfig } from "./space.config";

@Module({
  imports: [
    NestConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ".env",
      load: [
        () => ({ app: appConfig }),
        () => ({ mail: mailConfig, space: spaceConfig }),
      ],
      validationSchema: Joi.object({
        MONGODB_URI: Joi.string().required(),
        PORT: Joi.number().default(3000),
        NODE_ENV: Joi.string()
          .valid("development", "production", "test")
          .default("development"),
        JWT_SECRET: Joi.string().required(),
        JWT_EXPIRATION: Joi.string().default("1d"),
        CORS_ORIGIN: Joi.string().default("*"),
      }),
    }),
  ],
})
export class ConfigModule {}
