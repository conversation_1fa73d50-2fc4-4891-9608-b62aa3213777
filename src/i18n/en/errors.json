{"validation": {"invalid_email": "Invalid email format", "email_required": "Email is required", "password_required": "Password is required", "password_min_length": "Password must be at least {length} characters", "password_format": "Password must include uppercase, lowercase, and a number or special character", "password_must_be_string": "Password must be a string", "phone_number_required": "Phone number is required", "phone_number_format": "Invalid phone number format", "name_required": "Name is required", "name_must_be_string": "Name must be a string", "verification_code_required": "Verification code is required", "verification_code_length": "Verification code must be 6 characters", "website_url_required": "Website URL is required", "website_url_invalid": "Invalid website URL format", "logo_url_invalid": "Invalid logo URL format", "contact_email_invalid": "Invalid contact email format", "contact_phone_must_be_string": "Contact phone must be a string"}, "auth": {"email_exists": "Email is already in use", "not_found": "Registration information not found", "invalid_code": "Invalid verification code", "expired_code": "Verification code has expired", "used_code": "Verification code has already been used", "unauthorized": "Unauthorized", "invalid_password": "Invalid password", "user_not_verified": "User account is not verified", "login_failed": "<PERSON><PERSON> failed. Please try again.", "email_not_registered": "Email is not registered"}, "users": {"not_found": "User not found"}, "workspaces": {"not_found": "Workspace not found", "unauthorized": "You don't have permission to access this workspace", "creation_failed": "Failed to create workspace", "update_failed": "Failed to update workspace", "delete_failed": "Failed to delete workspace", "primary_required": "At least one primary workspace is required", "cannot_delete_primary_workspace": "Cannot delete primary workspace", "user_already_member": "User is already a member of this workspace", "member_not_found": "Workspace member not found", "add_member_failed": "Failed to add member to workspace", "update_member_failed": "Failed to update workspace member", "remove_member_failed": "Failed to remove workspace member", "find_members_failed": "Failed to find workspace members", "find_workspaces_failed": "Failed to find user's workspaces", "invalid_invitation_token": "Invalid or expired invitation token", "invitation_acceptance_failed": "Failed to accept workspace invitation", "invitation_not_found": "Invitation not found", "website_url_exists": "A workspace with this website URL already exists"}, "plugins": {"not_found": "Plugin not found", "find_failed": "Failed to find plugin", "not_enabled": "Plugin is not enabled for this workspace"}, "server": {"internal_error": "Internal server error", "not_found": "Resource not found"}}