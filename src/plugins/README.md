# Plugins Directory

This directory contains all plugins for the OnlyChat application. Plugins are automatically discovered and registered when the server starts.

## Auto-Registration

When the server starts, it automatically:

1. **Scans** the `src/plugins/` directory for subdirectories
2. **Looks** for an `index.ts` file in each plugin directory
3. **Imports** the plugin metadata from the default export
4. **Registers** the plugin in the database (or updates if already exists)

## Plugin Structure

Each plugin should have the following structure:

```
src/plugins/your-plugin/
├── index.ts              # Plugin metadata export
├── controllers/          # REST API controllers (optional)
├── services/            # Business logic services
├── schemas/             # Database schemas
├── dto/                 # Data transfer objects
├── guards/              # Custom guards (optional)
└── your-plugin.module.ts # NestJS module
```

## Plugin Metadata

The `index.ts` file should export plugin metadata as the default export:

```typescript
import { PluginCategory } from "../../modules/plugins/schemas/plugin.schema";
import { CreatePluginDto } from "../../modules/plugins/dto/create-plugin.dto";

export const yourPluginMetadata: CreatePluginDto = {
  key: "your-plugin",
  name: "Your Plugin Name",
  description: "Description of what your plugin does",
  version: "1.0.0",
  author: "Your Name",
  category: PluginCategory.UTILITY,
  iconUrl: "https://example.com/icon.png",
  documentationUrl: "https://docs.example.com/your-plugin",
  entryPoint: "src/plugins/your-plugin/index.js",
  isGloballyAvailable: true,
  isSystem: false,
  settingsSchema: {
    type: "object",
    properties: {
      // Define your plugin settings schema here
    },
    required: [],
  },
  defaultSettings: {
    // Default settings values
  },
  requiredPermissions: ["your-plugin:read", "your-plugin:write"],
};

export default yourPluginMetadata;
```

## Adding REST API Endpoints

If your plugin needs REST API endpoints:

1. **Create controllers** in the `controllers/` directory
2. **Add the controllers** to your plugin module
3. **Use guards** to validate plugin installation (see helpdesk example)
4. **Import your module** in `src/app.module.ts`

Example guard usage:

```typescript
@Controller("your-plugin/items")
@UseGuards(JwtAuthGuard, YourPluginGuard)
export class YourPluginController {
  // Your endpoints here
}
```

## Existing Plugins

- **helpdesk** - Knowledge base system with categories and articles

## Development Workflow

1. **Create** your plugin directory in `src/plugins/`
2. **Implement** your plugin following the structure above
3. **Build** the application: `npm run build`
4. **Start** the server: `npm run start`
5. **Plugin is auto-registered** and ready to use!

## Benefits of Auto-Registration

- ✅ **No manual scripts** needed to register plugins
- ✅ **Automatic discovery** of new plugins
- ✅ **Updates existing** plugins on server restart
- ✅ **Self-contained** - everything in one directory
- ✅ **Easy to remove** - just delete the plugin directory
- ✅ **Development friendly** - just add and restart

## Notes

- Plugins are registered during server startup
- The server needs to be built (`npm run build`) for auto-registration to work
- Plugin metadata is imported from the compiled JavaScript files in `dist/`
- If a plugin directory doesn't have a valid `index.ts` or metadata, it's skipped
