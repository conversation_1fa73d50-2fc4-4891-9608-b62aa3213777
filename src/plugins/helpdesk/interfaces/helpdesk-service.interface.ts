import { CreateCategoryDto } from "../dto/create-category.dto";
import { UpdateCategoryDto } from "../dto/update-category.dto";
import { PaginationResponseDto } from "src/modules/common/dto/pagination.dto";
import { ListArticlesDto } from "../dto/list-articles.dto";
import { CreateArticleDto } from "../dto/create-article.dto";
import { UpdateArticleDto } from "../dto/update-article.dto";
import { HelpDeskCategory } from "../entities/helpdesk-category.entity";
import { HelpDeskArticle } from "../entities/helpdesk-article.entity";
import { CreateSectionDto } from "../dto/create-section.dto";
import { HelpDeskCategorySection } from "../entities/helpdesk-category-section.entity";
import { UpdateSectionDto } from "../dto/update-section.dto";

export interface HelpdeskServiceInterface {
  createCategory(
    workspaceId: string,
    createDto: CreateCategoryDto
  ): Promise<HelpDeskCategory>;

  updateCategory(
    id: string,
    updateDto: UpdateCategoryDto
  ): Promise<HelpDeskCategory>;

  removeCategory(id: string, userId: string): Promise<boolean>;

  getCategories(workspaceId: string): Promise<HelpDeskCategory[]>;

  createSection(createDto: CreateSectionDto): Promise<HelpDeskCategorySection>;

  updateSection(
    id: string,
    updateDto: UpdateSectionDto
  ): Promise<HelpDeskCategorySection>;

  removeSection(id: string, userId: string): Promise<boolean>;

  getSections(categoryId: string): Promise<HelpDeskCategorySection[]>;

  addArticleToSection(
    articleId: string,
    sectionId: string
  ): Promise<HelpDeskArticle>;

  removeArticleFromSection(articleId: string): Promise<HelpDeskArticle>;

  createArticle(
    workspaceId: string,
    createDto: CreateArticleDto
  ): Promise<HelpDeskArticle>;

  updateArticle(
    id: string,
    updateDto: UpdateArticleDto
  ): Promise<HelpDeskArticle>;

  removeArticle(id: string, userId: string): Promise<boolean>;

  getArticles(
    workspaceId: string,
    inputDto: ListArticlesDto
  ): Promise<PaginationResponseDto<HelpDeskArticle>>;
}
