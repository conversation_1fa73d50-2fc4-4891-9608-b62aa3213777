import { Injectable } from "@nestjs/common";
import { appConfig } from "src/config/app.config";

export interface TranslationMap<T> {
  [languageCode: string]: T;
}

@Injectable()
export class TranslationService {
  /**
   * Get the best available translation for a given language
   * Falls back to default language if requested language is not available
   */
  getTranslation<T>(
    translations: Map<string, T> | TranslationMap<T>,
    requestedLanguage: string,
    defaultLanguage: string = appConfig.defaultLanguage
  ): T | null {
    // Convert Map to object if needed
    const translationsObj = translations instanceof Map 
      ? Object.fromEntries(translations) 
      : translations;

    // Try requested language first
    if (translationsObj[requestedLanguage]) {
      return translationsObj[requestedLanguage];
    }

    // Fall back to default language
    if (translationsObj[defaultLanguage]) {
      return translationsObj[defaultLanguage];
    }

    // Fall back to any available language
    const availableLanguages = Object.keys(translationsObj);
    if (availableLanguages.length > 0) {
      return translationsObj[availableLanguages[0]];
    }

    return null;
  }

  /**
   * Set translation for a specific language
   */
  setTranslation<T>(
    translations: Map<string, T>,
    language: string,
    translation: T
  ): Map<string, T> {
    translations.set(language, translation);
    return translations;
  }

  /**
   * Remove translation for a specific language
   */
  removeTranslation<T>(
    translations: Map<string, T>,
    language: string
  ): Map<string, T> {
    translations.delete(language);
    return translations;
  }

  /**
   * Get all available languages for a translation map
   */
  getAvailableLanguages<T>(
    translations: Map<string, T> | TranslationMap<T>
  ): string[] {
    if (translations instanceof Map) {
      return Array.from(translations.keys());
    }
    return Object.keys(translations);
  }

  /**
   * Check if a specific language is available
   */
  hasLanguage<T>(
    translations: Map<string, T> | TranslationMap<T>,
    language: string
  ): boolean {
    if (translations instanceof Map) {
      return translations.has(language);
    }
    return language in translations;
  }

  /**
   * Validate that required languages are present
   */
  validateRequiredLanguages<T>(
    translations: Map<string, T> | TranslationMap<T>,
    requiredLanguages: string[] = [appConfig.defaultLanguage]
  ): { isValid: boolean; missingLanguages: string[] } {
    const availableLanguages = this.getAvailableLanguages(translations);
    const missingLanguages = requiredLanguages.filter(
      lang => !availableLanguages.includes(lang)
    );

    return {
      isValid: missingLanguages.length === 0,
      missingLanguages
    };
  }

  /**
   * Create a translation map from legacy fields
   * Useful for migrating existing data
   */
  createTranslationFromLegacy<T>(
    legacyData: T,
    language: string = appConfig.defaultLanguage
  ): Map<string, T> {
    const translations = new Map<string, T>();
    translations.set(language, legacyData);
    return translations;
  }

  /**
   * Merge translation maps, with the second map taking precedence
   */
  mergeTranslations<T>(
    existing: Map<string, T>,
    updates: Map<string, T> | TranslationMap<T>
  ): Map<string, T> {
    const result = new Map(existing);
    
    if (updates instanceof Map) {
      updates.forEach((value, key) => {
        result.set(key, value);
      });
    } else {
      Object.entries(updates).forEach(([key, value]) => {
        result.set(key, value);
      });
    }
    
    return result;
  }

  /**
   * Convert Map to plain object for JSON serialization
   */
  mapToObject<T>(translations: Map<string, T>): TranslationMap<T> {
    return Object.fromEntries(translations);
  }

  /**
   * Convert plain object to Map
   */
  objectToMap<T>(translations: TranslationMap<T>): Map<string, T> {
    return new Map(Object.entries(translations));
  }

  /**
   * Get language from request headers or query parameters
   */
  getLanguageFromRequest(request: any): string {
    // Check query parameter first
    if (request.query?.lang && typeof request.query.lang === 'string') {
      const lang = request.query.lang.toLowerCase();
      if (appConfig.supportedLanguages.includes(lang)) {
        return lang;
      }
    }

    // Check header
    if (request.headers?.['accept-language']) {
      const headerLang = request.headers['accept-language']
        .split(',')[0]
        .split('-')[0]
        .toLowerCase();
      if (appConfig.supportedLanguages.includes(headerLang)) {
        return headerLang;
      }
    }

    // Check custom header
    if (request.headers?.['x-lang']) {
      const customLang = request.headers['x-lang'].toLowerCase();
      if (appConfig.supportedLanguages.includes(customLang)) {
        return customLang;
      }
    }

    return appConfig.defaultLanguage;
  }
}
