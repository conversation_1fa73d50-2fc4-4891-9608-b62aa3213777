import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { HelpdeskCategory } from "../schemas/helpdesk-category.schema";
import { HelpdeskArticle } from "../schemas/helpdesk-article.schema";
import { HelpdeskServiceInterface } from "../interfaces/helpdesk-service.interface";
import { PaginationResponseDto } from "src/modules/common/dto/pagination.dto";
import { CreateCategoryDto } from "../dto/create-category.dto";
import { ListArticlesDto } from "../dto/list-articles.dto";
import { UpdateCategoryDto } from "../dto/update-category.dto";
import { CreateArticleDto } from "../dto/create-article.dto";
import { UpdateArticleDto } from "../dto/update-article.dto";
import {
  HelpDeskCategory,
  HelpDeskCategoryDocument,
} from "../entities/helpdesk-category.entity";
import {
  HelpDeskArticle,
  HelpDeskArticleDocument,
} from "../entities/helpdesk-article.entity";
import { TranslationService } from "./translation.service";
import { appConfig } from "src/config/app.config";
import { CreateSectionDto } from "../dto/create-section.dto";
import {
  HelpDeskCategorySection,
  HelpDeskCategorySectionDocument,
} from "../entities/helpdesk-category-section.entity";
import { HelpdeskCategorySection } from "../schemas/helpdesk-category-section.schema";
import { UpdateSectionDto } from "../dto/update-section.dto";

@Injectable()
export class HelpdeskService implements HelpdeskServiceInterface {
  constructor(
    @InjectModel(HelpdeskCategory.name)
    private helpdeskCategoryModel: Model<HelpDeskCategoryDocument>,

    @InjectModel(HelpdeskArticle.name)
    private helpdeskArticleModel: Model<HelpDeskArticleDocument>,

    @InjectModel(HelpdeskCategorySection.name)
    private helpdeskCategorySectionModel: Model<HelpDeskCategorySectionDocument>,

    private translationService: TranslationService
  ) {}

  /**
   * Generate a unique slug for a category
   * @param baseSlug The base slug to make unique
   * @param workspaceId The workspace ID to check uniqueness within
   * @param excludeId Optional ID to exclude from uniqueness check (for updates)
   * @returns A unique slug
   */
  private async generateUniqueSlug(
    baseSlug: string,
    workspaceId: string,
    excludeId?: string
  ): Promise<string> {
    let slug = baseSlug;
    let counter = 1;

    while (true) {
      const query: any = {
        workspace: workspaceId,
        slug,
      };

      // For updates, exclude the current category from the check
      if (excludeId) {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
        query._id = { $ne: excludeId };
      }

      const existingCategory = await this.helpdeskCategoryModel
        .findOne(query)
        .exec();

      if (!existingCategory) {
        break;
      }

      slug = `${baseSlug}-${counter}`;
      counter++;
    }

    return slug;
  }

  /**
   * Generate slug from name if not provided
   * @param name The category name
   * @returns A slug generated from the name
   */
  private generateSlugFromName(name: string): string {
    return name
      .toLowerCase()
      .replace(/ /g, "-")
      .replace(/[^\w-]+/g, "");
  }

  async createCategory(
    workspaceId: string,
    createDto: CreateCategoryDto
  ): Promise<HelpDeskCategory> {
    // Validate that either translations or legacy fields are provided
    if (!createDto.translations && !createDto.name) {
      throw new Error("Either 'translations' or 'name' field is required");
    }

    // Handle backward compatibility and translations
    const categoryData: Record<string, any> = {
      workspace: workspaceId,
      defaultLanguage: createDto.defaultLanguage || appConfig.defaultLanguage,
    };

    // Handle translations
    if (createDto.translations) {
      categoryData.translations = new Map(
        Object.entries(createDto.translations)
      );

      // Use the first translation for legacy fields if not provided
      const firstLang = Object.keys(createDto.translations)[0];
      const firstTranslation = createDto.translations[firstLang];

      categoryData.name = createDto.name || firstTranslation.name;
      categoryData.desc = createDto.desc || firstTranslation.desc;
    } else {
      // Legacy mode - use provided name/desc
      if (!createDto.name) {
        throw new Error(
          "Category name is required when translations are not provided"
        );
      }
      categoryData.name = createDto.name;
      categoryData.desc = createDto.desc;

      // Create default translation from legacy fields
      categoryData.translations = new Map().set(categoryData.defaultLanguage, {
        name: createDto.name,
        desc: createDto.desc,
      });
    }

    // Handle other fields
    categoryData.image = createDto.image;

    // Generate slug from name if not provided
    if (!createDto.slug) {
      categoryData.slug = this.generateSlugFromName(categoryData.name);
    } else {
      categoryData.slug = createDto.slug;
    }

    // Ensure slug is unique
    categoryData.slug = await this.generateUniqueSlug(
      categoryData.slug,
      workspaceId
    );

    const category = new this.helpdeskCategoryModel(categoryData);
    const item = await category.save();
    return new HelpDeskCategory(item);
  }

  async updateCategory(
    id: string,
    updateDto: UpdateCategoryDto
  ): Promise<HelpDeskCategory> {
    // Get the existing category to find its workspace
    const existingCategory = await this.helpdeskCategoryModel
      .findById(id)
      .exec();
    if (!existingCategory) {
      throw new Error("Category not found");
    }
    // If slug is provided, ensure it's unique (excluding current category)
    if (updateDto.slug) {
      updateDto.slug = await this.generateUniqueSlug(
        updateDto.slug,
        (
          existingCategory.workspace as unknown as HelpDeskCategoryDocument
        )._id.toString(),
        id
      );
    }

    const updatedCategory = await this.helpdeskCategoryModel
      .findByIdAndUpdate(id, updateDto, { new: true })
      .exec();

    if (!updatedCategory) {
      throw new Error("Category not found");
    }

    return new HelpDeskCategory(updatedCategory);
  }
  async removeCategory(id: string, userId: string): Promise<boolean> {
    const result = await this.helpdeskCategoryModel
      .findByIdAndDelete(id)
      .exec();
    return !!result;
  }

  async getCategories(workspaceId: string): Promise<HelpDeskCategory[]> {
    const query = this.helpdeskCategoryModel
      .find({
        workspace: workspaceId,
      })
      .populate("sections");

    const items = await query.exec();

    return items.map((item) => {
      return new HelpDeskCategory(item);
    });
  }

  async createSection(
    createDto: CreateSectionDto
  ): Promise<HelpDeskCategorySection> {
    // Validate that either translations or legacy fields are provided
    if (!createDto.translations && !createDto.name) {
      throw new Error("Either 'translations' or 'name' field is required");
    }

    // Handle backward compatibility and translations
    const sectionData: Record<string, any> = {
      category: createDto.categoryId,
      defaultLanguage: createDto.defaultLanguage || appConfig.defaultLanguage,
    };

    // Handle translations
    if (createDto.translations) {
      sectionData.translations = new Map(
        Object.entries(createDto.translations)
      );

      // Use the first translation for legacy fields if not provided
      const firstLang = Object.keys(createDto.translations)[0];
      const firstTranslation = createDto.translations[firstLang];
      sectionData.name = createDto.name || firstTranslation.name;
    } else {
      // Legacy mode - use provided name
      if (!createDto.name) {
        throw new Error(
          "Section name is required when translations are not provided"
        );
      }
      sectionData.name = createDto.name;

      // Create default translation from legacy fields
      sectionData.translations = new Map().set(sectionData.defaultLanguage, {
        name: createDto.name,
      });
    }

    const section = new this.helpdeskCategorySectionModel(sectionData);
    const item = await section.save();
    return new HelpDeskCategorySection(item);
  }

  async updateSection(
    id: string,
    updateDto: UpdateSectionDto
  ): Promise<HelpDeskCategorySection> {
    const body: Record<string, any> = { ...updateDto };
    if (updateDto.categoryId) {
      body.category = updateDto.categoryId;
    }
    const updatedSection = await this.helpdeskCategorySectionModel
      .findByIdAndUpdate(id, body, { new: true })
      .exec();

    if (!updatedSection) {
      throw new Error("Section not found");
    }

    return new HelpDeskCategorySection(updatedSection);
  }

  async removeSection(id: string, userId: string): Promise<boolean> {
    const result = await this.helpdeskCategorySectionModel
      .findByIdAndDelete(id)
      .exec();
    return !!result;
  }

  async getSections(categoryId: string): Promise<HelpDeskCategorySection[]> {
    const sections = await this.helpdeskCategorySectionModel
      .find({ category: categoryId })
      .populate("articleCount")
      .exec();

    return sections.map((section) => new HelpDeskCategorySection(section));
  }

  async addArticleToSection(
    articleId: string,
    sectionId: string
  ): Promise<HelpDeskArticle> {
    // Verify that the section exists
    const section = await this.helpdeskCategorySectionModel
      .findById(sectionId)
      .exec();
    if (!section) {
      throw new Error("Section not found");
    }

    // Update the article to add it to the section
    const updatedArticle = await this.helpdeskArticleModel
      .findByIdAndUpdate(articleId, { section: sectionId }, { new: true })
      .exec();

    if (!updatedArticle) {
      throw new Error("Article not found");
    }

    return new HelpDeskArticle(updatedArticle);
  }

  async removeArticleFromSection(articleId: string): Promise<HelpDeskArticle> {
    // Remove the article from its current section
    const updatedArticle = await this.helpdeskArticleModel
      .findByIdAndUpdate(articleId, { $unset: { section: 1 } }, { new: true })
      .exec();

    if (!updatedArticle) {
      throw new Error("Article not found");
    }

    return new HelpDeskArticle(updatedArticle);
  }

  async createArticle(
    workspaceId: string,
    createDto: CreateArticleDto
  ): Promise<HelpDeskArticle> {
    // Validate that either translations or legacy fields are provided
    if (!createDto.translations && (!createDto.title || !createDto.content)) {
      throw new Error(
        "Either 'translations' or both 'title' and 'content' fields are required"
      );
    }

    // Handle backward compatibility and translations
    const articleData: Record<string, any> = {
      category: createDto.categoryId,
      workspace: workspaceId,
      defaultLanguage: createDto.defaultLanguage || appConfig.defaultLanguage,
      tags: createDto.tags || [],
      slug: createDto.slug,
      status: createDto.status,
    };

    // Add section if provided
    if (createDto.sectionId) {
      articleData.section = createDto.sectionId;
    }

    // Handle translations
    if (createDto.translations) {
      articleData.translations = new Map(
        Object.entries(createDto.translations)
      );

      // Use the first translation for legacy fields if not provided
      const firstLang = Object.keys(createDto.translations)[0];
      const firstTranslation = createDto.translations[firstLang];

      articleData.title = createDto.title || firstTranslation.title;
      articleData.content = createDto.content || firstTranslation.content;
    } else {
      // Legacy mode - use provided title/content
      if (!createDto.title || !createDto.content) {
        throw new Error(
          "Article title and content are required when translations are not provided"
        );
      }
      articleData.title = createDto.title;
      articleData.content = createDto.content;

      // Create default translation from legacy fields
      articleData.translations = new Map().set(articleData.defaultLanguage, {
        title: createDto.title,
        content: createDto.content,
      });
    }

    const article = new this.helpdeskArticleModel(articleData);
    const item = await article.save();
    return new HelpDeskArticle(item);
  }

  async updateArticle(
    id: string,
    updateDto: UpdateArticleDto
  ): Promise<HelpDeskArticle> {
    const body: Record<string, any> = { ...updateDto };
    if (updateDto.categoryId) {
      body.category = updateDto.categoryId;
    }
    if (updateDto.sectionId) {
      body.section = updateDto.sectionId;
    }
    const updatedArticle = await this.helpdeskArticleModel
      .findByIdAndUpdate(id, body, { new: true })
      .exec();

    if (!updatedArticle) {
      throw new Error("Article not found");
    }

    return new HelpDeskArticle(updatedArticle);
  }

  async removeArticle(id: string, userId: string): Promise<boolean> {
    const result = await this.helpdeskArticleModel.findByIdAndDelete(id).exec();
    return !!result;
  }

  async getArticles(
    workspaceId: string,
    inputDto: ListArticlesDto
  ): Promise<PaginationResponseDto<HelpDeskArticle>> {
    const { page, limit, keyword, categorySlug, status } = inputDto;
    const skip = (page - 1) * limit;

    const query: Record<string, any> = {
      workspace: workspaceId,
    };

    if (keyword) {
      query.$or = [
        { title: { $regex: keyword, $options: "i" } },
        { content: { $regex: keyword, $options: "i" } },
      ];
    }

    if (categorySlug) {
      const category = await this.helpdeskCategoryModel
        .findOne({
          workspace: workspaceId,
          slug: categorySlug,
        })
        .exec();
      if (category) {
        query.category = category._id;
      }
    }

    if (status) {
      query.status = status;
    }

    const [articles, total] = await Promise.all([
      this.helpdeskArticleModel.find(query).skip(skip).limit(limit).exec(),
      this.helpdeskArticleModel.countDocuments(query).exec(),
    ]);

    return {
      data: articles.map((item) => new HelpDeskArticle(item)),
      total,
      page: page,
      hasNextPage: total - page * limit > 0,
    };
  }
}
