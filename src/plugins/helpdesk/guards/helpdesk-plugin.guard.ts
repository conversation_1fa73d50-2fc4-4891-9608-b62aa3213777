import {
  Injectable,
  CanActivate,
  Execution<PERSON>ontext,
  ForbiddenException,
  BadRequestException,
  Inject,
} from "@nestjs/common";
import { WorkspacePluginServiceInterface } from "../../../modules/plugins/interfaces/workspace-plugin-service.interface";

@Injectable()
export class HelpdeskPluginGuard implements CanActivate {
  constructor(
    @Inject("WorkspacePluginServiceInterface")
    private readonly workspacePluginService: WorkspacePluginServiceInterface
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();

    // Get workspaceId from query parameters
    const workspaceId = request.query.workspaceId || request.user?.workspaceId;

    if (!workspaceId) {
      throw new BadRequestException("workspaceId query parameter is required");
    }

    // Check if the helpdesk plugin is installed and enabled for this workspace
    // const isEnabled = await this.workspacePluginService.isPluginEnabled(
    //   workspaceId,
    //   "helpdesk" // Plugin key
    // );
    const isEnabled = true;
    if (!isEnabled) {
      throw new ForbiddenException(
        "Helpdesk plugin is not installed or enabled for this workspace. Please install and enable the helpdesk plugin first."
      );
    }

    return true;
  }
}
