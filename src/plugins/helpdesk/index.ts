/**
 * Helpdesk Plugin for OnlyChat
 *
 * This plugin enables knowledge base functionality, allowing:
 * - Managing categories of help articles
 * - Creating and organizing help articles
 * - Searching knowledge base content
 * - Integrating knowledge base with chat
 */

import { PluginCategory } from "../../modules/plugins/schemas/plugin.schema";
import { CreatePluginDto } from "../../modules/plugins/dto/create-plugin.dto";

/**
 * Plugin metadata for auto-registration
 */
export const helpdeskPluginMetadata: CreatePluginDto = {
  key: "helpdesk",
  name: "Helpdesk Knowledge Base",
  description:
    "A knowledge base system for managing help articles and categories to support your customer service efforts.",
  version: "1.0.0",
  author: "OnlyChat Team",
  category: PluginCategory.UTILITY,
  iconUrl: "https://storage.cloud.com/plugins/helpdesk-icon.png", // Replace with actual icon URL
  documentationUrl: "https://docs.onlychat.com/plugins/helpdesk", // Replace with actual docs URL
  entryPoint: "src/plugins/helpdesk/index.js", // Path will be resolved at runtime
  isGloballyAvailable: true,
  isSystem: false,

  // Settings schema defines what configuration options the plugin accepts
  settingsSchema: {
    type: "object",
    properties: {
      workspaceId: {
        type: "string",
        description: "The workspace ID where the helpdesk will be active",
      },
      allowUserFeedback: {
        type: "boolean",
        description: "Allow users to rate articles as helpful/unhelpful",
      },
      enableAutoSuggestions: {
        type: "boolean",
        description: "Automatically suggest relevant articles during chat",
      },
      defaultCategoryOrder: {
        type: "array",
        items: { type: "string" },
        description: "Default order for main categories",
      },
      articleStatuses: {
        type: "array",
        items: {
          type: "string",
          enum: ["draft", "published", "archived"],
        },
        description: "Available article statuses",
      },
    },
    required: ["workspaceId"],
  },

  // Default settings that will be used if not overridden
  defaultSettings: {
    allowUserFeedback: true,
    enableAutoSuggestions: true,
    articleStatuses: ["draft", "published", "archived"],
    defaultCategoryOrder: [],
  },

  // Required permissions for using this plugin
  requiredPermissions: ["helpdesk:read", "helpdesk:write"],
};

// Export the plugin metadata for auto-registration
export default helpdeskPluginMetadata;
