import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";
import { HelpdeskCategory } from "./helpdesk-category.schema";

export enum ArticleStatus {
  DRAFT = "draft",
  PUBLISHED = "published",
  ARCHIVED = "archived",
}

// Translation interface for article fields
export interface ArticleTranslation {
  title: string;
  content: string;
}

// Create the translation schema separately
const ArticleTranslationSchema = new MongooseSchema(
  {
    title: { type: String, required: true },
    content: { type: String, required: true },
  },
  { _id: false }
);

@Schema({ timestamps: true })
export class HelpdeskArticle {
  // Legacy fields for backward compatibility
  @Prop({ required: true })
  title: string;

  @Prop({ required: true })
  content: string;

  @Prop({ required: true })
  slug: string;

  // New translation fields
  @Prop({
    type: Map,
    of: ArticleTranslationSchema,
    default: new Map(),
  })
  translations: Map<string, ArticleTranslation>;

  // Default language for this article
  @Prop({ default: "en" })
  defaultLanguage: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "HelpdeskCategory",
    required: true,
  })
  category: HelpdeskCategory;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "HelpdeskCategorySection",
    required: false,
  })
  section?: string;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({
    type: String,
    enum: ArticleStatus,
    default: ArticleStatus.DRAFT,
  })
  status: ArticleStatus;

  @Prop({ default: 0 })
  viewCount: number;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: true,
  })
  workspace: string;
}

export const HelpdeskArticleSchema =
  SchemaFactory.createForClass(HelpdeskArticle);
