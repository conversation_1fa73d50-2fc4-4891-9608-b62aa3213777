import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

// Translation interface for category fields
export interface CategoryTranslation {
  name: string;
  desc?: string;
}

// Create the translation schema separately
const CategoryTranslationSchema = new MongooseSchema(
  {
    name: { type: String, required: true },
    desc: { type: String, required: false },
  },
  { _id: false }
);

@Schema({ timestamps: true })
export class HelpdeskCategory {
  // Legacy fields for backward compatibility
  @Prop({ required: true })
  name: string;

  @Prop()
  image: string;

  @Prop({ required: true })
  slug: string;

  @Prop()
  desc: string;

  // New translation fields
  @Prop({
    type: Map,
    of: CategoryTranslationSchema,
    default: new Map(),
  })
  translations: Map<string, CategoryTranslation>;

  // Default language for this category
  @Prop({ default: "en" })
  defaultLanguage: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "Workspace",
    required: true,
  })
  workspace: string;
}

export const HelpdeskCategorySchema =
  SchemaFactory.createForClass(HelpdeskCategory);
