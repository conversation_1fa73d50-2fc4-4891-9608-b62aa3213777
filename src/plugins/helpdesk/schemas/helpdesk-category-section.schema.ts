import { <PERSON>p, Schem<PERSON>, SchemaFactory } from "@nestjs/mongoose";
import { Document, Schema as MongooseSchema } from "mongoose";

// Translation interface for category fields
export interface CategorySectionTranslation {
  name: string;
}

// Create the translation schema separately
const CategorySectionTranslationSchema = new MongooseSchema(
  {
    name: { type: String, required: true },
  },
  { _id: false }
);

@Schema({ timestamps: true })
export class HelpdeskCategorySection {
  // Legacy fields for backward compatibility
  @Prop({ required: true })
  name: string;

  // New translation fields
  @Prop({
    type: Map,
    of: CategorySectionTranslationSchema,
    default: new Map(),
  })
  translations: Map<string, CategorySectionTranslation>;

  // Default language for this section
  @Prop({ default: "en" })
  defaultLanguage: string;

  @Prop({
    type: MongooseSchema.Types.ObjectId,
    ref: "HelpdeskCategory",
    required: true,
  })
  category: string;
}

export const HelpdeskCategorySectionSchema = SchemaFactory.createForClass(
  HelpdeskCategorySection
);
