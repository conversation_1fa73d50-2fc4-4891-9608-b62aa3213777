import { <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { MongooseModule } from "@nestjs/mongoose";
import {
  HelpdeskCategory,
  HelpdeskCategorySchema,
} from "./schemas/helpdesk-category.schema";
import {
  HelpdeskArticle,
  HelpdeskArticleSchema,
} from "./schemas/helpdesk-article.schema";
import { HelpdeskService } from "./services/helpdesk.service";
import { TranslationService } from "./services/translation.service";
import { PluginsModule } from "../../modules/plugins/plugins.module";
import { HelpdeskController } from "./controllers/helpdesk.controller";
import {
  HelpdeskCategorySection,
  HelpdeskCategorySectionSchema,
} from "./schemas/helpdesk-category-section.schema";

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: HelpdeskCategory.name, schema: HelpdeskCategorySchema },
      { name: HelpdeskArticle.name, schema: HelpdeskArticleSchema },
      {
        name: HelpdeskCategorySection.name,
        schema: HelpdeskCategorySectionSchema,
      },
    ]),
    PluginsModule, // Import to access WorkspacePluginService
  ],
  controllers: [HelpdeskController],
  providers: [
    TranslationService,
    {
      provide: "HelpdeskServiceInterface",
      useClass: HelpdeskService,
    },
    HelpdeskService, // Also provide directly for controllers
  ],
  exports: ["HelpdeskServiceInterface", HelpdeskService, TranslationService],
})
export class HelpdeskModule {}
