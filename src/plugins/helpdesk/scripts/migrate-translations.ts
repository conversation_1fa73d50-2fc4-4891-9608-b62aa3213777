import { NestFactory } from '@nestjs/core';
import { AppModule } from '../../../app.module';
import { HelpdeskService } from '../services/helpdesk.service';
import { Model } from 'mongoose';
import { InjectModel } from '@nestjs/mongoose';
import { HelpdeskCategory } from '../schemas/helpdesk-category.schema';
import { HelpdeskArticle } from '../schemas/helpdesk-article.schema';
import { appConfig } from '../../../config/app.config';

/**
 * Migration script to convert existing helpdesk categories and articles
 * to use the new translation system.
 * 
 * This script:
 * 1. Finds all categories and articles without translations
 * 2. Creates default language translations from existing name/title/content/desc fields
 * 3. Updates the documents with the new translation structure
 * 
 * Usage:
 * npm run build
 * node dist/plugins/helpdesk/scripts/migrate-translations.js
 */

class TranslationMigrationService {
  constructor(
    @InjectModel(HelpdeskCategory.name)
    private helpdeskCategoryModel: Model<any>,
    
    @InjectModel(HelpdeskArticle.name)
    private helpdeskArticleModel: Model<any>
  ) {}

  async migrateCategoryTranslations(): Promise<void> {
    console.log('Starting category translation migration...');
    
    // Find categories without translations or with empty translations
    const categories = await this.helpdeskCategoryModel.find({
      $or: [
        { translations: { $exists: false } },
        { translations: { $size: 0 } },
        { translations: null }
      ]
    }).exec();

    console.log(`Found ${categories.length} categories to migrate`);

    for (const category of categories) {
      const defaultLang = category.defaultLanguage || appConfig.defaultLanguage;
      
      // Create translation from existing fields
      const translation = {
        name: category.name,
        desc: category.desc || undefined,
      };

      // Create translations map
      const translations = new Map();
      translations.set(defaultLang, translation);

      // Update the category
      await this.helpdeskCategoryModel.findByIdAndUpdate(
        category._id,
        {
          translations: translations,
          defaultLanguage: defaultLang,
        }
      ).exec();

      console.log(`Migrated category: ${category.name} (${category._id})`);
    }

    console.log('Category translation migration completed');
  }

  async migrateArticleTranslations(): Promise<void> {
    console.log('Starting article translation migration...');
    
    // Find articles without translations or with empty translations
    const articles = await this.helpdeskArticleModel.find({
      $or: [
        { translations: { $exists: false } },
        { translations: { $size: 0 } },
        { translations: null }
      ]
    }).exec();

    console.log(`Found ${articles.length} articles to migrate`);

    for (const article of articles) {
      const defaultLang = article.defaultLanguage || appConfig.defaultLanguage;
      
      // Create translation from existing fields
      const translation = {
        title: article.title,
        content: article.content,
      };

      // Create translations map
      const translations = new Map();
      translations.set(defaultLang, translation);

      // Update the article
      await this.helpdeskArticleModel.findByIdAndUpdate(
        article._id,
        {
          translations: translations,
          defaultLanguage: defaultLang,
        }
      ).exec();

      console.log(`Migrated article: ${article.title} (${article._id})`);
    }

    console.log('Article translation migration completed');
  }

  async validateMigration(): Promise<void> {
    console.log('Validating migration...');
    
    // Check categories
    const categoriesWithoutTranslations = await this.helpdeskCategoryModel.countDocuments({
      $or: [
        { translations: { $exists: false } },
        { translations: { $size: 0 } },
        { translations: null }
      ]
    }).exec();

    // Check articles
    const articlesWithoutTranslations = await this.helpdeskArticleModel.countDocuments({
      $or: [
        { translations: { $exists: false } },
        { translations: { $size: 0 } },
        { translations: null }
      ]
    }).exec();

    console.log(`Categories without translations: ${categoriesWithoutTranslations}`);
    console.log(`Articles without translations: ${articlesWithoutTranslations}`);

    if (categoriesWithoutTranslations === 0 && articlesWithoutTranslations === 0) {
      console.log('✅ Migration validation passed - all items have translations');
    } else {
      console.log('❌ Migration validation failed - some items still missing translations');
    }
  }

  async runMigration(): Promise<void> {
    try {
      console.log('🚀 Starting helpdesk translation migration...');
      console.log(`Default language: ${appConfig.defaultLanguage}`);
      console.log(`Supported languages: ${appConfig.supportedLanguages.join(', ')}`);
      
      await this.migrateCategoryTranslations();
      await this.migrateArticleTranslations();
      await this.validateMigration();
      
      console.log('✅ Migration completed successfully!');
    } catch (error) {
      console.error('❌ Migration failed:', error);
      throw error;
    }
  }
}

async function runMigration() {
  const app = await NestFactory.createApplicationContext(AppModule);
  
  try {
    const migrationService = app.get(TranslationMigrationService);
    await migrationService.runMigration();
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  } finally {
    await app.close();
  }
}

// Run migration if this file is executed directly
if (require.main === module) {
  runMigration();
}

export { TranslationMigrationService, runMigration };
