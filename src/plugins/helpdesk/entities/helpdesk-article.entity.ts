import { Types } from "mongoose";
import { WorkspaceDocument } from "src/modules/workspace/entities/workspace.entity";
import {
  BaseDocument,
  BaseEntity,
} from "src/modules/common/entities/base.entity";
import {
  ArticleStatus,
  ArticleTranslation,
} from "../schemas/helpdesk-article.schema";
import {
  HelpDeskCategory,
  HelpDeskCategoryDocument,
} from "./helpdesk-category.entity";
import { HelpDeskArticleType } from "../graphql/types/helpdesk-article.type";
import { appConfig } from "src/config/app.config";

abstract class BaseHelpDeskArticle extends BaseEntity {
  title!: string;
  content!: string;
  slug!: string;
  translations!: Map<string, ArticleTranslation>;
  defaultLanguage!: string;
  category?: HelpDeskCategoryDocument | HelpDeskCategory | null;
  categoryId?: string;
  section?: string;
  sectionId?: string;
  tags!: string[];
  status!: ArticleStatus;
  viewCount!: number;
  helpfulCount!: number;
  unhelpfulCount!: number;
  lastUpdatedBy?: string;
  workspace?: WorkspaceDocument;
  workspaceId?: string;
}

export interface HelpDeskArticleDocument
  extends BaseDocument<BaseHelpDeskArticle> {
  _id: Types.ObjectId;
}

export class HelpDeskArticle extends BaseHelpDeskArticle {
  constructor(doc?: HelpDeskArticleDocument) {
    super();
    if (doc) {
      super.assignFields(doc);

      this.title = doc.title;
      this.content = doc.content;
      this.slug = doc.slug;
      this.translations = doc.translations || new Map();
      this.defaultLanguage = doc.defaultLanguage || appConfig.defaultLanguage;
      this.categoryId = (
        doc.category as HelpDeskCategoryDocument
      )?._id?.toString();
      this.sectionId = doc.section?.toString();
      this.tags = doc.tags;
      this.status = doc.status;
      this.viewCount = doc.viewCount;
      this.helpfulCount = doc.helpfulCount;
      this.unhelpfulCount = doc.unhelpfulCount;
      this.lastUpdatedBy = doc.lastUpdatedBy;
    }
  }

  /**
   * Get translated content for a specific language
   */
  getTranslation(language: string): ArticleTranslation | null {
    // Try requested language first
    if (this.translations.has(language)) {
      return this.translations.get(language)!;
    }

    // Fall back to default language
    if (this.translations.has(this.defaultLanguage)) {
      return this.translations.get(this.defaultLanguage)!;
    }

    // Fall back to any available language
    const availableLanguages = Array.from(this.translations.keys());
    if (availableLanguages.length > 0) {
      return this.translations.get(availableLanguages[0])!;
    }

    // Fall back to legacy fields
    return {
      title: this.title,
      content: this.content,
    };
  }

  /**
   * Get localized title for a specific language
   */
  getLocalizedTitle(language: string = appConfig.defaultLanguage): string {
    const translation = this.getTranslation(language);
    return translation?.title || this.title;
  }

  /**
   * Get localized content for a specific language
   */
  getLocalizedContent(language: string = appConfig.defaultLanguage): string {
    const translation = this.getTranslation(language);
    return translation?.content || this.content;
  }

  /**
   * Get all available languages for this article
   */
  getAvailableLanguages(): string[] {
    return Array.from(this.translations.keys());
  }

  /**
   * Check if a specific language is available
   */
  hasLanguage(language: string): boolean {
    return this.translations.has(language);
  }

  toGraphQL(language?: string): HelpDeskArticleType {
    const articleType = new HelpDeskArticleType();
    articleType.id = this.id;
    articleType.slug = this.slug;
    articleType.categoryId = this.categoryId;
    articleType.sectionId = this.sectionId;
    articleType.tags = this.tags;
    articleType.status = this.status;
    articleType.viewCount = this.viewCount;
    articleType.workspaceId = this.workspaceId;
    articleType.createdAt = this.createdAt;
    articleType.updatedAt = this.updatedAt;

    // Use localized content if language is specified
    if (language) {
      articleType.title = this.getLocalizedTitle(language);
      articleType.content = this.getLocalizedContent(language);
    } else {
      articleType.title = this.title;
      articleType.content = this.content;
    }

    return articleType;
  }
}
