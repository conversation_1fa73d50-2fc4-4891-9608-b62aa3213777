import { Types } from "mongoose";
import {
  BaseDocument,
  BaseEntity,
} from "src/modules/common/entities/base.entity";
import { CategoryTranslation } from "../schemas/helpdesk-category.schema";
import { appConfig } from "src/config/app.config";
import { HelpDeskCategorySectionType } from "../graphql/types/helpdesk-category-section.type";

abstract class BaseHelpDeskCategorySection extends BaseEntity {
  name!: string;
  translations!: Map<string, CategoryTranslation>;
  defaultLanguage!: string;
  articleCount?: number;
}

export interface HelpDeskCategorySectionDocument
  extends BaseDocument<BaseHelpDeskCategorySection> {
  _id: Types.ObjectId;
}

export class HelpDeskCategorySection extends BaseHelpDeskCategorySection {
  constructor(doc?: HelpDeskCategorySectionDocument) {
    super();
    if (doc) {
      super.assignFields(doc);

      this.name = doc.name;
      this.translations = doc.translations || new Map();
      this.defaultLanguage = doc.defaultLanguage || appConfig.defaultLanguage;
      this.articleCount = (doc as any).articleCount || 0;
    }
  }

  /**
   * Get translated content for a specific language
   */
  getTranslation(language: string): CategoryTranslation | null {
    // Try requested language first
    if (this.translations.has(language)) {
      return this.translations.get(language)!;
    }

    // Fall back to default language
    if (this.translations.has(this.defaultLanguage)) {
      return this.translations.get(this.defaultLanguage)!;
    }

    // Fall back to any available language
    const availableLanguages = Array.from(this.translations.keys());
    if (availableLanguages.length > 0) {
      return this.translations.get(availableLanguages[0])!;
    }

    // Fall back to legacy fields
    return {
      name: this.name,
    };
  }

  /**
   * Get localized name for a specific language
   */
  getLocalizedName(language: string = appConfig.defaultLanguage): string {
    const translation = this.getTranslation(language);
    return translation?.name || this.name;
  }

  /**
   * Get all available languages for this category
   */
  getAvailableLanguages(): string[] {
    return Array.from(this.translations.keys());
  }

  /**
   * Check if a specific language is available
   */
  hasLanguage(language: string): boolean {
    return this.translations.has(language);
  }

  toGraphQL(language?: string): HelpDeskCategorySectionType {
    const sectionType = new HelpDeskCategorySectionType();
    sectionType.id = this.id;
    sectionType.articleCount = this.articleCount || 0;
    sectionType.createdAt = this.createdAt;
    sectionType.updatedAt = this.updatedAt;

    // Use localized content if language is specified
    if (language) {
      sectionType.name = this.getLocalizedName(language);
    } else {
      sectionType.name = this.name;
    }

    return sectionType;
  }
}
