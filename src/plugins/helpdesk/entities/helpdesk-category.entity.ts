import { Document, Types } from "mongoose";
import { WorkspaceDocument } from "src/modules/workspace/entities/workspace.entity";
import {
  BaseDocument,
  BaseEntity,
} from "src/modules/common/entities/base.entity";
import { HelpDeskCategoryType } from "../graphql/types/helpdesk-category.type";
import { CategoryTranslation } from "../schemas/helpdesk-category.schema";
import { appConfig } from "src/config/app.config";
import { HelpDeskCategorySection } from "./helpdesk-category-section.entity";

abstract class BaseHelpDeskCategory extends BaseEntity {
  name!: string;
  image?: string;
  slug!: string;
  desc?: string;
  translations!: Map<string, CategoryTranslation>;
  defaultLanguage!: string;
  workspace?: WorkspaceDocument;
  workspaceId?: string;
  sections?: HelpDeskCategorySection[];
}

export interface HelpDeskCategoryDocument
  extends BaseDocument<BaseHelpDeskCategory> {
  _id: Types.ObjectId;
}

export class HelpDeskCategory extends BaseHelpDeskCategory {
  constructor(doc?: HelpDeskCategoryDocument & { sections?: any[] }) {
    super();
    if (doc) {
      super.assignFields(doc);

      this.name = doc.name;
      this.image = doc.image;
      this.slug = doc.slug;
      this.desc = doc.desc;
      this.translations = doc.translations || new Map();
      this.defaultLanguage = doc.defaultLanguage || appConfig.defaultLanguage;
      this.workspaceId = doc.workspace?._id.toString();

      // Handle populated sections
      if (doc.sections) {
        this.sections = doc.sections.map(
          (section: any) => new HelpDeskCategorySection(section)
        );
      }
    }
  }

  /**
   * Get translated content for a specific language
   */
  getTranslation(language: string): CategoryTranslation | null {
    // Try requested language first
    if (this.translations.has(language)) {
      return this.translations.get(language)!;
    }

    // Fall back to default language
    if (this.translations.has(this.defaultLanguage)) {
      return this.translations.get(this.defaultLanguage)!;
    }

    // Fall back to any available language
    const availableLanguages = Array.from(this.translations.keys());
    if (availableLanguages.length > 0) {
      return this.translations.get(availableLanguages[0])!;
    }

    // Fall back to legacy fields
    return {
      name: this.name,
      desc: this.desc,
    };
  }

  /**
   * Get localized name for a specific language
   */
  getLocalizedName(language: string = appConfig.defaultLanguage): string {
    const translation = this.getTranslation(language);
    return translation?.name || this.name;
  }

  /**
   * Get localized description for a specific language
   */
  getLocalizedDesc(
    language: string = appConfig.defaultLanguage
  ): string | undefined {
    const translation = this.getTranslation(language);
    return translation?.desc || this.desc;
  }

  /**
   * Get all available languages for this category
   */
  getAvailableLanguages(): string[] {
    return Array.from(this.translations.keys());
  }

  /**
   * Check if a specific language is available
   */
  hasLanguage(language: string): boolean {
    return this.translations.has(language);
  }

  toGraphQL(language?: string): HelpDeskCategoryType {
    const categoryType = new HelpDeskCategoryType();
    categoryType.id = this.id;
    categoryType.image = this.image;
    categoryType.slug = this.slug;
    categoryType.workspaceId = this.workspaceId;
    categoryType.createdAt = this.createdAt;
    categoryType.updatedAt = this.updatedAt;

    // Use localized content if language is specified
    if (language) {
      categoryType.name = this.getLocalizedName(language);
      categoryType.desc = this.getLocalizedDesc(language);
    } else {
      categoryType.name = this.name;
      categoryType.desc = this.desc;
    }

    return categoryType;
  }
}
