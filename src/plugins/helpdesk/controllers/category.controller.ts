import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
  Request,
} from "@nestjs/common";
import { HelpdeskPluginGuard } from "../guards/helpdesk-plugin.guard";
import { HelpdeskService } from "../services/helpdesk.service";
import { TranslationService } from "../services/translation.service";
import {
  ApiBearerAuth,
  ApiBody,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiResponse,
  ApiTags,
} from "@nestjs/swagger";
import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { CreateArticleDto } from "../dto/create-article.dto";
import { CreateCategoryDto } from "../dto/create-category.dto";
import { UpdateArticleDto } from "../dto/update-article.dto";
import { UpdateCategoryDto } from "../dto/update-category.dto";
import { ListArticlesDto } from "../dto/list-articles.dto";
import { CreateSectionDto } from "../dto/create-section.dto";
import { UpdateSectionDto } from "../dto/update-section.dto";
import { JwtAuthWorkspaceGuard } from "src/modules/auth/guards/jwt-auth-workspace.guard";

@ApiTags("Helpdesk/Category")
@Controller("helpdesk/category")
@UseGuards(JwtAuthWorkspaceGuard, HelpdeskPluginGuard)
@ApiBearerAuth()
export class HelpdeskController {
  constructor(
    private readonly helpdeskService: HelpdeskService,
    private readonly translationService: TranslationService
  ) {}

  @Get("categories")
  @ApiOperation({ summary: "Get all categories for a workspace" })
  @ApiQuery({
    name: "lang",
    description: "Language code for localized content",
    required: false,
    example: "en",
  })
  @ApiResponse({ status: 200, description: "List of categories" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getCategories(
    @Request() req: RequestWithUser,
    @Query("lang") lang?: string
  ) {
    const categories = await this.helpdeskService.getCategories(
      req.user.workspaceId!
    );

    // If language is specified, return localized content
    if (lang) {
      return categories.map((category) => ({
        ...category,
        name: category.getLocalizedName(lang),
        desc: category.getLocalizedDesc(lang),
        sections: category.sections?.map((section) => ({
          ...section,
          name: section.getLocalizedName(lang),
        })),
      }));
    }

    return categories;
  }

  @Post("categories")
  @ApiOperation({ summary: "Create a new helpdesk category" })
  @ApiBody({ type: CreateCategoryDto })
  @ApiResponse({ status: 201, description: "Category created successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async createCategory(
    @Body() createCategoryDto: CreateCategoryDto,
    @Request() req: RequestWithUser
  ) {
    try {
      return await this.helpdeskService.createCategory(
        req.user.workspaceId!,
        createCategoryDto
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Put("categories/:id")
  @ApiOperation({ summary: "Update a helpdesk category" })
  @ApiParam({
    name: "id",
    description: "Category ID",
    required: true,
  })
  @ApiBody({ type: UpdateCategoryDto })
  @ApiResponse({ status: 200, description: "Category updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async updateCategory(
    @Param("id") id: string,
    @Body() updateCategoryDto: UpdateCategoryDto
  ) {
    try {
      return await this.helpdeskService.updateCategory(id, updateCategoryDto);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Delete("categories/:id")
  @ApiOperation({ summary: "Delete a helpdesk category" })
  @ApiParam({
    name: "id",
    description: "Category ID",
    required: true,
  })
  @ApiResponse({ status: 200, description: "Category deleted successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async deleteCategory(
    @Param("id") id: string,
    @Request() req: RequestWithUser
  ) {
    try {
      return await this.helpdeskService.removeCategory(id, req.user.sub);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Post("sections")
  @ApiOperation({ summary: "Create a new helpdesk section" })
  @ApiBody({ type: CreateSectionDto })
  @ApiResponse({ status: 201, description: "Section created successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async createSection(@Body() createSectionDto: CreateSectionDto) {
    try {
      return await this.helpdeskService.createSection(createSectionDto);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Put("sections/:id")
  @ApiOperation({ summary: "Update a helpdesk section" })
  @ApiParam({
    name: "id",
    description: "Section ID",
    required: true,
  })
  @ApiBody({ type: UpdateSectionDto })
  @ApiResponse({ status: 200, description: "Section updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async updateSection(
    @Param("id") id: string,
    @Body() updateSectionDto: UpdateSectionDto
  ) {
    try {
      return await this.helpdeskService.updateSection(id, updateSectionDto);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Delete("sections/:id")
  @ApiOperation({ summary: "Delete a helpdesk section" })
  @ApiParam({
    name: "id",
    description: "Section ID",
    required: true,
  })
  @ApiResponse({ status: 200, description: "Section deleted successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async deleteSection(
    @Param("id") id: string,
    @Request() req: RequestWithUser
  ) {
    try {
      return await this.helpdeskService.removeSection(id, req.user.sub);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Get("categories/:categoryId/sections")
  @ApiOperation({
    summary: "Get all sections for a category with article counts",
  })
  @ApiParam({
    name: "categoryId",
    description: "Category ID",
    required: true,
  })
  @ApiQuery({
    name: "lang",
    description: "Language code for localized content",
    required: false,
    example: "en",
  })
  @ApiResponse({
    status: 200,
    description: "List of sections with article counts",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getSections(
    @Param("categoryId") categoryId: string,
    @Query("lang") lang?: string
  ) {
    try {
      const sections = await this.helpdeskService.getSections(categoryId);

      // If language is specified, return localized content
      if (lang) {
        return sections.map((section) => ({
          ...section,
          name: section.getLocalizedName(lang),
        }));
      }

      return sections;
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Post("sections/:sectionId/articles")
  @ApiOperation({ summary: "Add an article to a section" })
  @ApiParam({
    name: "sectionId",
    description: "Section ID",
    required: true,
  })
  @ApiBody({
    schema: {
      type: "object",
      properties: {
        articleId: {
          type: "string",
          description: "Article ID to add to the section",
          example: "507f1f77bcf86cd799439011",
        },
      },
      required: ["articleId"],
    },
  })
  @ApiResponse({
    status: 200,
    description: "Article added to section successfully",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async addArticleToSection(
    @Param("sectionId") sectionId: string,
    @Body("articleId") articleId: string
  ) {
    try {
      return await this.helpdeskService.addArticleToSection(
        articleId,
        sectionId
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Delete("articles/:articleId/section")
  @ApiOperation({ summary: "Remove an article from its section" })
  @ApiParam({
    name: "articleId",
    description: "Article ID",
    required: true,
  })
  @ApiResponse({
    status: 200,
    description: "Article removed from section successfully",
  })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async removeArticleFromSection(@Param("articleId") articleId: string) {
    try {
      return await this.helpdeskService.removeArticleFromSection(articleId);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Get("articles")
  @ApiOperation({ summary: "Get all articles for a workspace" })
  @ApiQuery({
    name: "lang",
    description: "Language code for localized content",
    required: false,
    example: "en",
  })
  @ApiResponse({ status: 200, description: "List of articles" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async getArticles(
    @Query() inputDto: ListArticlesDto,
    @Request() req: RequestWithUser,
    @Query("lang") lang?: string
  ) {
    const result = await this.helpdeskService.getArticles(
      req.user.workspaceId!,
      inputDto
    );

    // If language is specified, return localized content
    if (lang) {
      return {
        ...result,
        data: result.data.map((article) => ({
          ...article,
          title: article.getLocalizedTitle(lang),
          content: article.getLocalizedContent(lang),
        })),
      };
    }

    return result;
  }

  @Post("articles")
  @ApiOperation({ summary: "Create a new helpdesk article" })
  @ApiBody({ type: CreateArticleDto })
  @ApiResponse({ status: 201, description: "Article created successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async createArticle(
    @Body() createArticleDto: CreateArticleDto,
    @Request() req: RequestWithUser
  ) {
    try {
      return await this.helpdeskService.createArticle(
        req.user.workspaceId!,
        createArticleDto
      );
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Put("articles/:id")
  @ApiOperation({ summary: "Update a helpdesk article" })
  @ApiParam({
    name: "id",
    description: "Article ID",
    required: true,
  })
  @ApiBody({ type: UpdateArticleDto })
  @ApiResponse({ status: 200, description: "Article updated successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async updateArticle(
    @Param("id") id: string,
    @Body() updateArticleDto: UpdateArticleDto
  ) {
    try {
      return await this.helpdeskService.updateArticle(id, updateArticleDto);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }

  @Delete("articles/:id")
  @ApiOperation({ summary: "Delete a helpdesk article" })
  @ApiParam({
    name: "id",
    description: "Article ID",
    required: true,
  })
  @ApiResponse({ status: 200, description: "Article deleted successfully" })
  @ApiResponse({ status: 400, description: "Bad request" })
  @ApiResponse({ status: 401, description: "Unauthorized" })
  async deleteArticle(
    @Param("id") id: string,
    @Request() req: RequestWithUser
  ) {
    try {
      return await this.helpdeskService.removeArticle(id, req.user.sub);
    } catch (error) {
      throw new BadRequestException(
        error instanceof Error ? error.message : "Unknown error"
      );
    }
  }
}
