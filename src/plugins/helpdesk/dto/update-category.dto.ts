import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsObject } from "class-validator";
import { CategoryTranslation } from "../schemas/helpdesk-category.schema";

export class UpdateCategoryDto {
  // Legacy fields for backward compatibility
  @ApiProperty({
    description: "Category name (legacy field, use translations instead)",
    example: "Getting Started",
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: "Category slug",
    example: "getting-started",
    required: false,
  })
  @IsOptional()
  @IsString()
  slug?: string;

  @ApiProperty({
    description:
      "Category description (legacy field, use translations instead)",
    example: "Getting Started",
    required: false,
  })
  @IsOptional()
  @IsString()
  desc?: string;

  @ApiProperty({
    description: "Category image",
    example: "https://storage.cloud.com/categories/getting-started.png",
    required: false,
  })
  @IsOptional()
  @IsString()
  image?: string;

  @ApiProperty({
    description: "Translations for different languages",
    example: {
      en: { name: "Getting Started", desc: "Learn the basics" },
      vi: { name: "<PERSON><PERSON><PERSON> đầu", desc: "<PERSON><PERSON><PERSON> nh<PERSON>ng điều cơ bản" },
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  translations?: Record<string, CategoryTranslation>;

  @ApiProperty({
    description: "Default language for this category",
    example: "en",
    required: false,
  })
  @IsOptional()
  @IsString()
  defaultLanguage?: string;
}
