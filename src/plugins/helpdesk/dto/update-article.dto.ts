import { ApiProperty } from "@nestjs/swagger";
import {
  IsString,
  IsOptional,
  IsArray,
  IsEnum,
  IsMongoId,
  IsObject,
} from "class-validator";
import {
  ArticleStatus,
  ArticleTranslation,
} from "../schemas/helpdesk-article.schema";

export class UpdateArticleDto {
  // Legacy fields for backward compatibility
  @ApiProperty({
    description: "Article title (legacy field, use translations instead)",
    example: "How to create your first workspace",
    required: false,
  })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({
    description: "Article content (legacy field, use translations instead)",
    example:
      "# Getting Started\n\nFollow these steps to create your first workspace...",
    required: false,
  })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiProperty({
    description: "Category ID this article belongs to",
    example: "507f1f77bcf86cd799439011",
    required: false,
  })
  @IsOptional()
  @IsMongoId()
  categoryId?: string;

  @ApiProperty({
    description: "Section ID this article belongs to (optional)",
    example: "507f1f77bcf86cd799439012",
    required: false,
  })
  @IsOptional()
  @IsMongoId()
  sectionId?: string;

  @ApiProperty({
    description: "Translations for different languages",
    example: {
      en: {
        title: "How to create your first workspace",
        content: "# Getting Started\n\nFollow these steps...",
      },
      vi: {
        title: "Cách tạo workspace đầu tiên",
        content: "# Bắt đầu\n\nThực hiện các bước sau...",
      },
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  translations?: Record<string, ArticleTranslation>;

  @ApiProperty({
    description: "Default language for this article",
    example: "en",
    required: false,
  })
  @IsOptional()
  @IsString()
  defaultLanguage?: string;

  @ApiProperty({
    description: "Tags for the article",
    example: ["beginner", "workspace", "setup"],
    required: false,
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[];

  @ApiProperty({
    description: "Article slug",
    example: "how-to-create-your-first-workspace",
    required: false,
  })
  @IsOptional()
  @IsString()
  slug?: string;

  @ApiProperty({
    description: "Publication status of the article",
    enum: ArticleStatus,
    example: ArticleStatus.PUBLISHED,
    required: false,
  })
  @IsOptional()
  @IsEnum(ArticleStatus)
  status?: ArticleStatus;
}
