import { ApiProperty } from "@nestjs/swagger";
import { IsMongoId } from "class-validator";

export class AddArticleToSectionDto {
  @ApiProperty({
    description: "Article ID to add to the section",
    example: "507f1f77bcf86cd799439011",
  })
  @IsMongoId()
  articleId: string;

  @ApiProperty({
    description: "Section ID to add the article to",
    example: "507f1f77bcf86cd799439012",
  })
  @IsMongoId()
  sectionId: string;
}
