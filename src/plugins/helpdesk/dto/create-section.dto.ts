import { ApiProperty } from "@nestjs/swagger";
import { IsString, IsOptional, IsObject } from "class-validator";
import { CategoryTranslation } from "../schemas/helpdesk-category.schema";

export class CreateSectionDto {
  // Legacy fields for backward compatibility
  @ApiProperty({
    description: "Category name (legacy field, use translations instead)",
    example: "Getting Started",
    required: false,
  })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({
    description: "Category ID this section belongs to",
    example: "",
  })
  @IsString()
  categoryId: string;

  @ApiProperty({
    description: "Translations for different languages",
    example: {
      en: { name: "Getting Started" },
      vi: { name: "Bắt đầu" },
    },
    required: false,
  })
  @IsOptional()
  @IsObject()
  translations?: Record<string, CategoryTranslation>;

  @ApiProperty({
    description: "Default language for this category",
    example: "en",
    required: false,
  })
  @IsOptional()
  @IsString()
  defaultLanguage?: string;
}
