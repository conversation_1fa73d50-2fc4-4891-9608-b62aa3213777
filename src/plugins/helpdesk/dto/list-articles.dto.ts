import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString } from "class-validator";
import { PaginationInputDto } from "src/modules/common/dto/pagination.dto";

export class ListArticlesDto extends PaginationInputDto {
  @ApiProperty({
    description: "Keyword to search for",
    required: false,
    example: "",
  })
  @IsString()
  @IsOptional()
  keyword?: string;

  @ApiProperty({
    description: "Category slug to filter by",
    required: false,
    example: "",
  })
  @IsString()
  @IsOptional()
  categorySlug?: string;

  @ApiProperty({
    description: "Status to filter by",
    required: false,
    example: "published",
  })
  @IsString()
  @IsOptional()
  status?: string;
}
