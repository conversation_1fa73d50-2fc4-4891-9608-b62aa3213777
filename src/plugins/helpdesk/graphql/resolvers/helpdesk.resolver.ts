import { Resolver } from "@nestjs/graphql";
import { Inject } from "@nestjs/common";
import { PubSub } from "graphql-subscriptions";

import { HelpDeskCategoryType } from "../types/helpdesk-category.type";
import { HelpdeskServiceInterface } from "../../interfaces/helpdesk-service.interface";

// Create PubSub instance for subscriptions
const pubSub = new PubSub();

@Resolver(() => HelpDeskCategoryType)
export class HelpDeskResolver {
  constructor(
    @Inject("HelpdeskServiceInterface")
    private readonly helpdeskService: HelpdeskServiceInterface
  ) {}
}
