import { Field, ID, Int, ObjectType } from "@nestjs/graphql";

import { Node } from "src/modules/graphql/types/node.interface";
import { WorkspaceType } from "src/modules/workspace/graphql/types/workspace.type";
import { PaginationType } from "src/modules/graphql/types/pagination.type";

@ObjectType("HelpDeskArticle", { implements: [Node] })
export class HelpDeskArticleType implements Node {
  @Field(() => ID)
  id: string;

  @Field(() => WorkspaceType, { nullable: true })
  workspace?: WorkspaceType;

  @Field(() => String)
  title: string;

  @Field(() => String)
  content: string;

  @Field(() => String)
  slug: string;

  // Hidden field to store the category ID for the resolver
  categoryId?: string;

  // Hidden field to store the section ID for the resolver
  sectionId?: string;

  @Field(() => [String])
  tags: string[];

  @Field(() => String)
  status: string;

  @Field(() => Int)
  viewCount: number;

  // Hidden field to store the workspace ID for the resolver
  workspaceId?: string;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;
}

@ObjectType()
export class HelpDeskArticlePagination extends PaginationType(
  HelpDeskArticleType
) {}
