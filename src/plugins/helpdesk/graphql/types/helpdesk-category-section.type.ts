import { Field, ID, Int, ObjectType } from "@nestjs/graphql";

import { Node } from "src/modules/graphql/types/node.interface";
import { PaginationType } from "src/modules/graphql/types/pagination.type";

@ObjectType("HelpDeskCategorySection", { implements: [Node] })
export class HelpDeskCategorySectionType implements Node {
  @Field(() => ID)
  id: string;

  @Field(() => String)
  name: string;

  @Field(() => Int, { defaultValue: 0 })
  articleCount: number;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;
}

@ObjectType()
export class HelpDeskCategorySectionPagination extends PaginationType(
  HelpDeskCategorySectionType
) {}
