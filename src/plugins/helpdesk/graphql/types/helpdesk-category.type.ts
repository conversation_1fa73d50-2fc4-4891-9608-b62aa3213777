import { Field, ID, ObjectType } from "@nestjs/graphql";

import { Node } from "src/modules/graphql/types/node.interface";
import { WorkspaceType } from "src/modules/workspace/graphql/types/workspace.type";
import { PaginationType } from "src/modules/graphql/types/pagination.type";

@ObjectType("HelpDeskCategory", { implements: [Node] })
export class HelpDeskCategoryType implements Node {
  @Field(() => ID)
  id: string;

  @Field(() => String)
  name: string;

  @Field(() => String, { nullable: true })
  image?: string;

  @Field(() => String)
  slug: string;

  @Field(() => String, { nullable: true })
  desc?: string;

  @Field(() => WorkspaceType, { nullable: true })
  workspace?: WorkspaceType;

  // Hidden field to store the workspace ID for the resolver
  workspaceId?: string;

  @Field(() => Date, { nullable: true })
  createdAt?: Date;

  @Field(() => Date, { nullable: true })
  updatedAt?: Date;
}

@ObjectType()
export class HelpDeskCategoryPagination extends PaginationType(
  HelpDeskCategoryType
) {}
