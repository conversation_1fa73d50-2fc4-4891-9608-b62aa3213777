import { IoAdapter } from "@nestjs/platform-socket.io";
import { ServerOptions, Server } from "socket.io";
import { createAdapter } from "@socket.io/redis-adapter";
import { Redis as RedisClient } from "ioredis";
import { INestApplicationContext, Logger, Inject } from "@nestjs/common";
import { REDIS_PUB, REDIS_SUB } from "../services/redis/redis.constants";

export class RedisIoAdapter extends IoAdapter {
  private readonly logger = new Logger(RedisIoAdapter.name);
  private adapterConstructor: ReturnType<typeof createAdapter> | null = null;

  constructor(
    app: INestApplicationContext,
    @Inject(REDIS_PUB) private readonly pubClient: RedisClient,
    @Inject(REDIS_SUB) private readonly subClient: RedisClient
  ) {
    super(app);
  }

  connectToRedis(): void {
    try {
      // Check if Redis clients are available
      if (!this.pubClient || !this.subClient) {
        this.logger.warn(
          "Redis clients are not available. WebSocket scaling will be limited."
        );
        return;
      }

      // Create the Redis adapter using the injected clients
      this.adapterConstructor = createAdapter(this.pubClient, this.subClient);
      this.logger.log("Redis adapter configured successfully");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`Failed to configure Redis adapter: ${errorMessage}`);
    }
  }

  createIOServer(port: number, options?: ServerOptions): Server {
    const server = super.createIOServer(port, options) as Server;

    if (this.adapterConstructor) {
      server.adapter(this.adapterConstructor);
      this.logger.log("Redis adapter initialized");
    } else {
      this.logger.warn(
        "Redis adapter not initialized, falling back to in-memory adapter"
      );
    }

    return server;
  }

  // Clean up Redis connections on application shutdown
  async close(server: Server): Promise<void> {
    // Note: Redis clients are managed by the Redis module providers
    // They will be cleaned up automatically when the module is destroyed
    this.logger.log("Redis adapter cleanup completed");
    return super.close(server);
  }
}
