import { Controller, Get, Request } from "@nestjs/common";
import { AppService } from "./app.service";
import { ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { AnalyticsService } from "./common/services/analytics.service";

import { Request as ExpressRequest } from "express";

@ApiTags("App")
@Controller()
export class AppController {
  constructor(
    private readonly appService: AppService,
    private readonly analyticsService: AnalyticsService
  ) {}

  //i want to the api to get analytics data
  @Get("analytics")
  @ApiOperation({ summary: "Get analytics data" })
  @ApiResponse({
    status: 200,
    description: "Analytics data retrieved successfully",
  })
  getAnalytics(@Request() req: ExpressRequest) {
    const analyticsData = this.analyticsService.extractAnalyticsData(req);
    return analyticsData;
  }
}
