import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from "@nestjs/common";
import { GqlExecutionContext } from "@nestjs/graphql";
import { Observable } from "rxjs";
import { tap, catchError } from "rxjs/operators";
import { ConfigService } from "@nestjs/config";

@Injectable()
export class GraphQLLoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger("GraphQL-Resolver");
  private readonly logResolverDetails: boolean;

  constructor(private readonly configService: ConfigService) {
    this.logResolverDetails =
      this.configService.get("LOG_GRAPHQL_RESOLVER_DETAILS", "false") ===
      "true";
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    // Only process GraphQL contexts
    if (context.getType<string>() !== "graphql") {
      return next.handle();
    }

    const gqlContext = GqlExecutionContext.create(context);
    const info = gqlContext.getInfo() as any;
    const args = gqlContext.getArgs() as any;
    const startTime = Date.now();

    // Extract resolver information
    const resolverName = context.getClass().name;
    const handlerName = context.getHandler().name;
    const fieldName = info?.fieldName || "unknown";
    const parentType = info?.parentType?.name || "unknown";
    const returnType = info?.returnType?.toString() || "unknown";

    // Get user info from context
    const ctx = gqlContext.getContext() as any;
    const userId = ctx?.req?.user?.sub;
    const userInfo = userId ? ` [User: ${userId}]` : "";

    if (this.logResolverDetails) {
      this.logger.debug(
        `→ Resolver ${resolverName}.${handlerName} (${parentType}.${fieldName}) -> ${returnType}${userInfo}`
      );

      if (Object.keys(args).length > 0) {
        this.logger.debug(`  Args: ${JSON.stringify(this.sanitizeArgs(args))}`);
      }
    }

    return next.handle().pipe(
      tap(() => {
        const responseTime = Date.now() - startTime;

        if (this.logResolverDetails) {
          this.logger.debug(
            `← ✓ Resolver ${resolverName}.${handlerName} completed in ${responseTime}ms${userInfo}`
          );
        }
      }),
      catchError((error: any) => {
        const responseTime = Date.now() - startTime;

        this.logger.error(
          `← ✗ Resolver ${resolverName}.${handlerName} failed in ${responseTime}ms: ${error?.message || "Unknown error"}${userInfo}`
        );

        if (this.logResolverDetails) {
          this.logger.error(
            `  Error details: ${error?.stack || "No stack trace"}`
          );
        }

        throw error;
      })
    );
  }

  private sanitizeArgs(args: any): any {
    if (!args || typeof args !== "object") {
      return args;
    }

    const sanitized = { ...args };

    // Remove sensitive fields
    const sensitiveFields = ["password", "token", "secret", "key", "auth"];

    const sanitizeObject = (obj: any): any => {
      if (!obj || typeof obj !== "object") {
        return obj;
      }

      const result = Array.isArray(obj) ? [] : {};

      for (const [key, value] of Object.entries(obj)) {
        if (sensitiveFields.includes(key.toLowerCase())) {
          result[key] = "[REDACTED]";
        } else if (typeof value === "object") {
          result[key] = sanitizeObject(value);
        } else {
          result[key] = value;
        }
      }

      return result;
    };

    return sanitizeObject(sanitized);
  }
}
