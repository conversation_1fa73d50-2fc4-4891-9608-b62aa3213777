import {
  Injectable,
  NestInterceptor,
  Execution<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Lo<PERSON>,
} from "@nestjs/common";
import { Observable } from "rxjs";
import { tap, catchError } from "rxjs/operators";
import { Request, Response } from "express";
import { ConfigService } from "@nestjs/config";
import { ApiLoggerService, ApiLogEntry } from "../services/api-logger.service";

interface LogData {
  timestamp: string;
  method: string;
  url: string;
  userAgent?: string;
  ip: string;
  userId?: string;
  workspaceId?: string;
  statusCode: number;
  responseTime: number;
  requestBody?: any;
  responseBody?: any;
  error?: string;
  headers?: Record<string, string>;
  query?: Record<string, any>;
  params?: Record<string, any>;
}

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  private readonly logger = new Logger("API");
  private readonly detailedLogging: boolean;
  private readonly logRequestBody: boolean;
  private readonly logResponseBody: boolean;
  private readonly logHeaders: boolean;

  constructor(
    private readonly configService: ConfigService,
    private readonly apiLoggerService: ApiLoggerService
  ) {
    // Configure logging options from environment variables
    this.detailedLogging =
      this.configService.get("LOG_DETAILED", "false") === "true";
    this.logRequestBody =
      this.configService.get("LOG_REQUEST_BODY", "false") === "true";
    this.logResponseBody =
      this.configService.get("LOG_RESPONSE_BODY", "false") === "true";
    this.logHeaders = this.configService.get("LOG_HEADERS", "false") === "true";
  }

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const startTime = Date.now();
    const request = context.switchToHttp().getRequest<Request>();
    const response = context.switchToHttp().getResponse<Response>();

    // Skip logging for certain paths
    if (!request || this.shouldSkipLogging(request.url)) {
      return next.handle();
    }

    const logData: Partial<LogData> = {
      timestamp: new Date().toISOString(),
      method: request.method,
      url: request.url,
      userAgent: request.get("User-Agent"),
      ip: this.getClientIp(request),
      userId: (request as any).user?.sub || (request as any).user?.id,
      workspaceId: (request as any).user?.workspaceId,
    };

    // Add detailed information if enabled
    if (this.detailedLogging) {
      if (this.logHeaders) {
        logData.headers = this.sanitizeHeaders(request.headers);
      }
      logData.query = request.query;
      logData.params = request.params;

      if (this.logRequestBody && request.body) {
        logData.requestBody = this.sanitizeRequestBody(request.body);
      }
    }

    return next.handle().pipe(
      tap((responseBody) => {
        const responseTime = Date.now() - startTime;

        const completeLogData: LogData = {
          ...logData,
          statusCode: response.statusCode,
          responseTime,
        } as LogData;

        if (this.detailedLogging && this.logResponseBody && responseBody) {
          completeLogData.responseBody =
            this.sanitizeResponseBody(responseBody);
        }

        // Fire and forget logging to avoid blocking the response
        this.logRequest(completeLogData).catch((error) => {
          this.logger.error("Failed to log request:", error);
        });
      }),
      catchError((error) => {
        const responseTime = Date.now() - startTime;

        const errorLogData: LogData = {
          ...logData,
          statusCode: error.status || 500,
          responseTime,
          error: error.message || "Unknown error",
        } as LogData;

        // Fire and forget logging to avoid blocking the error response
        this.logError(errorLogData).catch((logError) => {
          this.logger.error("Failed to log error:", logError);
        });
        throw error;
      })
    );
  }

  private shouldSkipLogging(url: string): boolean {
    const skipPaths = [
      "/health",
      "/metrics",
      "/favicon.ico",
      "/api/docs",
      "/docs",
    ];

    return skipPaths.some((path) => url.includes(path));
  }

  private getClientIp(request: Request): string {
    return (
      (request.headers["x-forwarded-for"] as string) ||
      (request.headers["x-real-ip"] as string) ||
      request.connection.remoteAddress ||
      request.socket.remoteAddress ||
      "unknown"
    );
  }

  private sanitizeHeaders(headers: any): Record<string, string> {
    const sanitized = { ...headers };

    // Remove sensitive headers
    const sensitiveHeaders = [
      "authorization",
      "cookie",
      "x-api-key",
      "x-auth-token",
    ];
    sensitiveHeaders.forEach((header) => {
      if (sanitized[header]) {
        sanitized[header] = "[REDACTED]";
      }
    });

    return sanitized;
  }

  private sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== "object") {
      return body;
    }

    const sanitized = { ...body };

    // Remove sensitive fields
    const sensitiveFields = ["password", "token", "secret", "key", "auth"];
    sensitiveFields.forEach((field) => {
      if (sanitized[field]) {
        sanitized[field] = "[REDACTED]";
      }
    });

    return sanitized;
  }

  private sanitizeResponseBody(body: any): any {
    if (!body || typeof body !== "object") {
      return body;
    }

    // Limit response body size for logging
    const bodyString = JSON.stringify(body);
    if (bodyString.length > 1000) {
      return "[RESPONSE_TOO_LARGE]";
    }

    return body;
  }

  private async logRequest(logData: LogData): Promise<void> {
    // Convert LogData to ApiLogEntry format
    const apiLogEntry: ApiLogEntry = {
      timestamp: logData.timestamp,
      method: logData.method,
      url: logData.url,
      statusCode: logData.statusCode,
      responseTime: logData.responseTime,
      ip: logData.ip,
      userAgent: logData.userAgent,
      userId: logData.userId,
      workspaceId: logData.workspaceId,
      requestBody: logData.requestBody,
      responseBody: logData.responseBody,
      headers: logData.headers,
      query: logData.query,
      params: logData.params,
    };

    // Use the ApiLoggerService for comprehensive logging
    await this.apiLoggerService.logApiRequest(apiLogEntry);
  }

  private async logError(logData: LogData): Promise<void> {
    // Convert LogData to ApiLogEntry format
    const apiLogEntry: ApiLogEntry = {
      timestamp: logData.timestamp,
      method: logData.method,
      url: logData.url,
      statusCode: logData.statusCode,
      responseTime: logData.responseTime,
      ip: logData.ip,
      userAgent: logData.userAgent,
      userId: logData.userId,
      workspaceId: logData.workspaceId,
      error: logData.error,
      requestBody: logData.requestBody,
      headers: logData.headers,
      query: logData.query,
      params: logData.params,
    };

    // Use the ApiLoggerService for comprehensive logging
    await this.apiLoggerService.logApiRequest(apiLogEntry);
  }
}
