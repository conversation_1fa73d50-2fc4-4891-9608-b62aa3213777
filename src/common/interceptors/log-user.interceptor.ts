import {
  <PERSON><PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from "@nestjs/common";
import { Observable, tap } from "rxjs";

import { RequestWithUser } from "src/modules/auth/interfaces/jwt-payload.interface";
import { AnalyticsService } from "src/common/services/analytics.service";

@Injectable()
export class LogUserInterceptor implements NestInterceptor {
  constructor(private readonly analyticsService: AnalyticsService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const req = context.switchToHttp().getRequest<RequestWithUser>();

    if (req.user && req.user.sub) {
      // Use AnalyticsService to extract all analytics data for console logging
      const analyticsData = this.analyticsService.extractAnalyticsData(req);

      console.log("User log data:", {
        ...analyticsData,
        path: req.originalUrl || req.url,
        method: req.method,
        userId: req.user.sub,
        email: req.user.email || null,
        userResolutionMethod: "jwt_sub",
      });
    }

    return next.handle().pipe(tap(() => {}));
  }

  // const logData = new this.userLogModel({
  //   ip,
  //   country: geo.country || null,
  //   city: geo.city || null,
  //   region: geo.region || null,
  //   browser: agent.family,
  //   os: agent.os.toString(),
  //   device: agent.device.toString(),
  //   timestamp: new Date(),
  //   path: req.originalUrl,
  //   method: req.method,
  //   userId: user?.id || null, // <-- Attach userId here
  // });

  // return next.handle().pipe(
  //   tap(() => {
  //     logData.save().catch(() => {});
  //   })
  // );
}
