import { Injectable, Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import * as fs from "fs";
import * as path from "path";

export interface ApiLogEntry {
  timestamp: string;
  method: string;
  url: string;
  statusCode: number;
  responseTime: number;
  ip: string;
  userAgent?: string;
  userId?: string;
  workspaceId?: string;
  requestBody?: any;
  responseBody?: any;
  error?: string;
  headers?: Record<string, string>;
  query?: Record<string, any>;
  params?: Record<string, any>;
}

@Injectable()
export class ApiLoggerService {
  private readonly logger = new Logger(ApiLoggerService.name);
  private readonly logToFile: boolean;
  private readonly logToConsole: boolean;
  private readonly logDirectory: string;

  constructor(private readonly configService: ConfigService) {
    this.logToFile = this.configService.get("LOG_TO_FILE", "false") === "true";
    this.logToConsole =
      this.configService.get("LOG_TO_CONSOLE", "true") === "true";
    this.logDirectory = this.configService.get("LOG_DIRECTORY", "./logs");

    if (this.logToFile) {
      this.ensureLogDirectory();
    }
  }

  async logApiRequest(logEntry: ApiLogEntry): Promise<void> {
    try {
      if (this.logToConsole) {
        this.logToConsoleOutput(logEntry);
      }

      if (this.logToFile) {
        await this.logToFileOutput(logEntry);
      }

      // You can add more logging destinations here:
      // - Database logging
      // - External logging services (e.g., Elasticsearch, Splunk)
      // - Metrics collection (e.g., Prometheus)
    } catch (error) {
      this.logger.error("Failed to log API request:", error);
    }
  }

  private logToConsoleOutput(logEntry: ApiLogEntry): void {
    const {
      method,
      url,
      statusCode,
      responseTime,
      userId,
      workspaceId,
      error,
    } = logEntry;

    const baseMessage = `${method} ${url} ${statusCode} ${responseTime}ms`;
    const userInfo = userId ? ` [User: ${userId}]` : "";
    const workspaceInfo = workspaceId ? ` [Workspace: ${workspaceId}]` : "";
    const message = `${baseMessage}${userInfo}${workspaceInfo}`;

    if (error) {
      this.logger.error(`${message} - ERROR: ${error}`);
    } else if (statusCode >= 400) {
      this.logger.warn(message);
    } else {
      this.logger.log(message);
    }
  }

  private async logToFileOutput(logEntry: ApiLogEntry): Promise<void> {
    const today = new Date().toISOString().split("T")[0]; // YYYY-MM-DD
    const logFileName = `api-requests-${today}.log`;
    const logFilePath = path.join(this.logDirectory, logFileName);

    const logLine = JSON.stringify(logEntry) + "\n";

    try {
      await fs.promises.appendFile(logFilePath, logLine, "utf8");
    } catch (error) {
      this.logger.error(`Failed to write to log file ${logFilePath}:`, error);
    }
  }

  private ensureLogDirectory(): void {
    try {
      if (!fs.existsSync(this.logDirectory)) {
        fs.mkdirSync(this.logDirectory, { recursive: true });
        this.logger.log(`Created log directory: ${this.logDirectory}`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to create log directory ${this.logDirectory}:`,
        error
      );
    }
  }

  // Method to get log statistics
  async getLogStats(date?: string): Promise<any> {
    if (!this.logToFile) {
      return { error: "File logging is not enabled" };
    }

    const targetDate = date || new Date().toISOString().split("T")[0];
    const logFileName = `api-requests-${targetDate}.log`;
    const logFilePath = path.join(this.logDirectory, logFileName);

    try {
      if (!fs.existsSync(logFilePath)) {
        return { error: "Log file not found for the specified date" };
      }

      const logContent = await fs.promises.readFile(logFilePath, "utf8");
      const logLines = logContent
        .trim()
        .split("\n")
        .filter((line) => line);

      const stats = {
        totalRequests: logLines.length,
        successfulRequests: 0,
        errorRequests: 0,
        averageResponseTime: 0,
        methodStats: {} as Record<string, number>,
        statusCodeStats: {} as Record<string, number>,
        topEndpoints: {} as Record<string, number>,
      };

      let totalResponseTime = 0;

      logLines.forEach((line) => {
        try {
          const logEntry = JSON.parse(line) as ApiLogEntry;

          // Count successful vs error requests
          if (logEntry.statusCode >= 400) {
            stats.errorRequests++;
          } else {
            stats.successfulRequests++;
          }

          // Calculate response time
          totalResponseTime += logEntry.responseTime;

          // Method statistics
          stats.methodStats[logEntry.method] =
            (stats.methodStats[logEntry.method] || 0) + 1;

          // Status code statistics
          const statusGroup = `${Math.floor(logEntry.statusCode / 100)}xx`;
          stats.statusCodeStats[statusGroup] =
            (stats.statusCodeStats[statusGroup] || 0) + 1;

          // Top endpoints
          const endpoint = `${logEntry.method} ${logEntry.url.split("?")[0]}`;
          stats.topEndpoints[endpoint] =
            (stats.topEndpoints[endpoint] || 0) + 1;
        } catch {
          // Skip invalid log lines
        }
      });

      stats.averageResponseTime = totalResponseTime / logLines.length;

      return stats;
    } catch (error) {
      this.logger.error(`Failed to read log stats:`, error);
      return { error: "Failed to read log file" };
    }
  }

  // Method to clean up old log files
  async cleanupOldLogs(daysToKeep: number = 30): Promise<void> {
    if (!this.logToFile) {
      return;
    }

    try {
      const files = await fs.promises.readdir(this.logDirectory);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      for (const file of files) {
        if (file.startsWith("api-requests-") && file.endsWith(".log")) {
          const dateMatch = file.match(/api-requests-(\d{4}-\d{2}-\d{2})\.log/);
          if (dateMatch) {
            const fileDate = new Date(dateMatch[1]);
            if (fileDate < cutoffDate) {
              const filePath = path.join(this.logDirectory, file);
              await fs.promises.unlink(filePath);
              this.logger.log(`Deleted old log file: ${file}`);
            }
          }
        }
      }
    } catch (error) {
      this.logger.error("Failed to cleanup old logs:", error);
    }
  }
}
