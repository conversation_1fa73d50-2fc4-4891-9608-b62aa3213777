import { Logger } from "@nestjs/common";
import { ConfigService } from "@nestjs/config";
import { ApiLoggerService } from "../services/api-logger.service";

// Factory function to create GraphQL logging plugin
export function createGraphQLLoggingPlugin(
  configService: ConfigService,
  apiLoggerService: ApiLoggerService
) {
  const logger = new Logger("GraphQL");
  const logGraphQLQueries =
    configService.get("LOG_GRAPHQL_QUERIES", "true") === "true";
  const logGraphQLVariables =
    configService.get("LOG_GRAPHQL_VARIABLES", "false") === "true";
  const logGraphQLResponses =
    configService.get("LOG_GRAPHQL_RESPONSES", "false") === "true";
  const logGraphQLErrors =
    configService.get("LOG_GRAPHQL_ERRORS", "true") === "true";

  return {
    async requestDidStart() {
      const startTime = Date.now();

      return {
        willSendResponse: async (requestContext: any) => {
          const endTime = Date.now();
          const responseTime = endTime - startTime;

          try {
            await logGraphQLRequest(requestContext, responseTime);
          } catch (error) {
            logger.error("Failed to log GraphQL request:", error);
          }
        },

        didEncounterErrors: async (requestContext: any) => {
          if (logGraphQLErrors) {
            const endTime = Date.now();
            const responseTime = endTime - startTime;

            try {
              await logGraphQLError(requestContext, responseTime);
            } catch (error) {
              logger.error("Failed to log GraphQL error:", error);
            }
          }
        },
      };
    },
  };

  async function logGraphQLRequest(
    requestContext: any,
    responseTime: number
  ): Promise<void> {
    const { request, response, contextValue } = requestContext;

    // Extract operation info
    const operationName = request.operationName || "Unknown";
    const operationType = extractOperationType(request.query);
    const statusCode = response?.http?.status || 200;

    // Extract user info
    const userId = contextValue?.req?.user?.sub;
    const workspaceId = contextValue?.req?.user?.workspaceId;
    const ip = contextValue?.req?.ip || "unknown";
    const userAgent = contextValue?.req?.headers?.["user-agent"];

    // Create log entry
    const logEntry = {
      timestamp: new Date().toISOString(),
      method: "GRAPHQL",
      url: `/graphql/${operationType}/${operationName}`,
      statusCode,
      responseTime,
      ip,
      userAgent,
      userId,
      workspaceId,
      requestBody: buildRequestBody(request),
      responseBody: logGraphQLResponses
        ? sanitizeResponse(response?.data)
        : undefined,
      query: {
        operationName,
        operationType,
      },
    };

    // Log to console
    const userInfo = userId ? ` [User: ${userId}]` : "";
    const workspaceInfo = workspaceId ? ` [Workspace: ${workspaceId}]` : "";
    const message = `GraphQL ${operationType} ${operationName} ${statusCode} ${responseTime}ms${userInfo}${workspaceInfo}`;

    if (statusCode >= 400) {
      logger.warn(message);
    } else {
      logger.log(message);
    }

    // Log to file/database
    await apiLoggerService.logApiRequest(logEntry);
  }

  async function logGraphQLError(
    requestContext: any,
    responseTime: number
  ): Promise<void> {
    const { request, response, contextValue } = requestContext;

    const operationName = request.operationName || "Unknown";
    const operationType = extractOperationType(request.query);
    const errors = response?.errors || [];
    const userId = contextValue?.req?.user?.sub;
    const workspaceId = contextValue?.req?.user?.workspaceId;
    const ip = contextValue?.req?.ip || "unknown";

    const errorMessages = errors.map((error: any) => error.message).join(", ");

    const logEntry = {
      timestamp: new Date().toISOString(),
      method: "GRAPHQL",
      url: `/graphql/${operationType}/${operationName}`,
      statusCode: 400,
      responseTime,
      ip,
      userId,
      workspaceId,
      error: errorMessages,
      requestBody: buildRequestBody(request),
      query: {
        operationName,
        operationType,
        errors: errors.map((error: any) => ({
          message: error.message,
          path: error.path,
          locations: error.locations,
        })),
      },
    };

    const userInfo = userId ? ` [User: ${userId}]` : "";
    logger.error(
      `GraphQL ${operationType} ${operationName} ERROR: ${errorMessages}${userInfo}`
    );

    await apiLoggerService.logApiRequest(logEntry);
  }

  function extractOperationType(query?: string): string {
    if (!query) return "unknown";

    const trimmedQuery = query.trim();
    if (trimmedQuery.startsWith("query")) return "query";
    if (trimmedQuery.startsWith("mutation")) return "mutation";
    if (trimmedQuery.startsWith("subscription")) return "subscription";

    // Default to query if no explicit type
    return "query";
  }

  function buildRequestBody(request: any): any {
    const body: any = {};

    if (logGraphQLQueries && request.query) {
      body.query = request.query;
    }

    if (logGraphQLVariables && request.variables) {
      body.variables = sanitizeVariables(request.variables);
    }

    if (request.operationName) {
      body.operationName = request.operationName;
    }

    return Object.keys(body).length > 0 ? body : undefined;
  }

  function sanitizeVariables(
    variables: Record<string, any>
  ): Record<string, any> {
    const sanitized = { ...variables };

    // Remove sensitive fields
    const sensitiveFields = ["password", "token", "secret", "key", "auth"];
    sensitiveFields.forEach((field) => {
      if (sanitized[field]) {
        sanitized[field] = "[REDACTED]";
      }
    });

    return sanitized;
  }

  function sanitizeResponse(data: any): any {
    if (!data || typeof data !== "object") {
      return data;
    }

    // Limit response size for logging
    const dataString = JSON.stringify(data);
    if (dataString.length > 2000) {
      return "[RESPONSE_TOO_LARGE]";
    }

    return data;
  }
}
