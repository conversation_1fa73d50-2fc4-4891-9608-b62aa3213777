import { Test, TestingModule } from "@nestjs/testing";
import { ConfigService } from "@nestjs/config";
import { createGraphQLLoggingPlugin } from "./graphql-logging.plugin";
import { ApiLoggerService } from "../services/api-logger.service";

describe("GraphQLLoggingPlugin", () => {
  let plugin: any;
  let configService: ConfigService;
  let apiLoggerService: ApiLoggerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: ConfigService,
          useValue: {
            get: jest.fn((key: string, defaultValue?: string) => {
              const config = {
                LOG_GRAPHQL_QUERIES: "true",
                LOG_GRAPHQL_VARIABLES: "false",
                LOG_GRAPHQL_RESPONSES: "false",
                LOG_GRAPHQL_ERRORS: "true",
              };
              return config[key] || defaultValue;
            }),
          },
        },
        {
          provide: ApiLoggerService,
          useValue: {
            logApiRequest: jest.fn().mockResolvedValue(undefined),
          },
        },
      ],
    }).compile();

    configService = module.get<ConfigService>(ConfigService);
    apiLoggerService = module.get<ApiLoggerService>(ApiLoggerService);
    plugin = createGraphQLLoggingPlugin(configService, apiLoggerService);
  });

  it("should be defined", () => {
    expect(plugin).toBeDefined();
  });

  it("should have requestDidStart method", () => {
    expect(plugin.requestDidStart).toBeDefined();
    expect(typeof plugin.requestDidStart).toBe("function");
  });

  it("should return request lifecycle hooks", async () => {
    const hooks = await plugin.requestDidStart();
    expect(hooks).toBeDefined();
    expect(hooks.willSendResponse).toBeDefined();
    expect(hooks.didEncounterErrors).toBeDefined();
  });
});

describe("GraphQLLoggingInterceptor", () => {
  it("should be importable", () => {
    const {
      GraphQLLoggingInterceptor,
    } = require("../interceptors/graphql-logging.interceptor");
    expect(GraphQLLoggingInterceptor).toBeDefined();
  });
});
