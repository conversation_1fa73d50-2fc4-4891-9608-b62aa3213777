import { Module, Global } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ApiLoggerService } from "../services/api-logger.service";
import { LoggingInterceptor } from "../interceptors/logging.interceptor";
import { GraphQLLoggingInterceptor } from "../interceptors/graphql-logging.interceptor";

@Global()
@Module({
  imports: [ConfigModule],
  providers: [ApiLoggerService, LoggingInterceptor, GraphQLLoggingInterceptor],
  exports: [ApiLoggerService, LoggingInterceptor, GraphQLLoggingInterceptor],
})
export class LoggingModule {}
