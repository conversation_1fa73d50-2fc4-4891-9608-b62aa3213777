import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Lo<PERSON>,
  ExecutionContext,
  ContextType,
} from "@nestjs/common";
import { Request, Response } from "express";
import { I18nService } from "nestjs-i18n";
import { appConfig } from "../../config";
import { GqlExecutionContext } from "@nestjs/graphql";

interface ErrorResponse {
  statusCode: number;
  message: string | string[];
  error: string;
}

interface GraphQLContext {
  req: Request;
}

interface GraphQLInfo {
  path: {
    key: string;
    typename: string;
  };
}

@Catch()
export class I18nExceptionFilter implements ExceptionFilter {
  constructor(private readonly i18n: I18nService) {}

  async catch(exception: unknown, host: ArgumentsHost) {
    // Check if the request is a GraphQL request
    const type = host.getType();
    const isGraphQL = type === ("graphql" as ContextType);

    if (isGraphQL) {
      return exception;
    }

    // Handle HTTP request
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();

    // Determine language from request (query param, header, etc.)
    const lang = this.getLanguageFromRequest(request);

    let statusCode: number;
    let errorMessage: string | string[];

    // Create response with additional debug info
    let responseBody: Record<string, any> = {};

    if (exception instanceof HttpException) {
      const errorResponse = exception.getResponse() as ErrorResponse;
      statusCode = exception.getStatus();
      errorMessage = errorResponse.message || exception.message;
    } else {
      statusCode = HttpStatus.INTERNAL_SERVER_ERROR;
      errorMessage = "errors.server.internal_error";
    }

    // Translate error messages
    const translatedMessage = await this.translateErrorMessage(
      errorMessage,
      lang
    );

    responseBody = { ...responseBody, statusCode, message: translatedMessage };
    response.status(statusCode).json(responseBody);
  }

  private getLanguageFromRequest(request: Request): string {
    // Get language from request
    let requestedLang = appConfig.defaultLanguage;

    // Check query parameter
    const queryLang = request?.query?.lang;
    if (queryLang && typeof queryLang === "string") {
      requestedLang = queryLang.toLowerCase();
    } else {
      // Check header (Accept-Language)
      const headerLang = request?.headers["accept-language"];
      if (headerLang) {
        // Extract the primary language code (e.g., 'en-US' -> 'en')
        requestedLang = headerLang.split(",")[0].split("-")[0].toLowerCase();
      }
    }

    // Validate if the requested language is supported
    if (appConfig.supportedLanguages.includes(requestedLang)) {
      return requestedLang;
    }

    // Return default language if not supported
    return appConfig.defaultLanguage;
  }

  private async translateErrorMessage(
    errorMessage: string | string[],
    lang: string
  ): Promise<string | string[]> {
    if (Array.isArray(errorMessage)) {
      return Promise.all(
        errorMessage.map(
          async (message) => await this.translateSingleMessage(message, lang)
        )
      );
    }

    return this.translateSingleMessage(errorMessage, lang);
  }

  private async translateSingleMessage(
    message: string,
    lang: string
  ): Promise<string> {
    // Check if the message is a translation key (simplified check)
    if (message.includes(".") && !message.includes(" ")) {
      try {
        // Try to translate the message key
        return await this.i18n.translate(message, { lang });
      } catch (error) {
        console.log(error);

        return message;
      }
    }

    // For messages that are not translation keys, return as is
    return message;
  }
}
