import { ValidationPipe, ValidationError } from "@nestjs/common";
import { I18nService } from "nestjs-i18n";
import { I18nHelper } from "../utils/i18n.helper";

export class I18nValidationPipe extends ValidationPipe {
  constructor(private readonly i18n: I18nService) {
    super({
      transform: true,
      whitelist: false,
      forbidNonWhitelisted: true,
      exceptionFactory: (errors: ValidationError[]) =>
        I18nHelper.badRequest(this.formatErrors(errors)),
    });
  }

  private formatErrors(errors: ValidationError[]): string[] {
    return errors.flatMap((error) => this.mapChildrenErrors(error));
  }

  private mapChildrenErrors(error: ValidationError): string[] {
    if (error.children && error.children.length > 0) {
      return error.children.flatMap((childError) =>
        this.mapChildrenErrors(childError)
      );
    }

    const messages: string[] = [];

    if (error.constraints) {
      for (const key in error.constraints) {
        // Convert validator error to i18n key - we'll use this later to translate
        // For example: 'errors.validation.invalid_email' for email format errors
        let i18nKey: string;
        const property = error.property;

        if (key === "isEmail") {
          i18nKey = "errors.validation.invalid_email";
        } else if (key === "isNotEmpty") {
          i18nKey = `errors.validation.${property}_required`;
        } else if (key === "minLength") {
          i18nKey = `errors.validation.${property}_min_length`;
        } else if (key === "isLength" && property === "code") {
          i18nKey = "errors.validation.verification_code_length";
        } else if (key === "matches" && property === "password") {
          i18nKey = "errors.validation.password_format";
        } else if (key === "matches" && property === "phoneNumber") {
          i18nKey = "errors.validation.phone_number_format";
        } else {
          // Fallback for messages that don't have a specific translation
          i18nKey = error.constraints[key];
        }

        messages.push(i18nKey);
      }
    }

    return messages;
  }
}
