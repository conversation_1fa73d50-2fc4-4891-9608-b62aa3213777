import {
  registerDecorator,
  ValidationOptions,
  ValidationArguments,
  ValidatorConstraint,
  ValidatorConstraintInterface,
} from "class-validator";
import { Injectable } from "@nestjs/common";
import { InjectModel } from "@nestjs/mongoose";
import { Model } from "mongoose";
import { User } from "../../modules/users/schemas/user.schema";

@ValidatorConstraint({ name: "IsUserExists", async: true })
@Injectable()
export class IsUserExistsConstraint implements ValidatorConstraintInterface {
  constructor(@InjectModel(User.name) private userModel: Model<User>) {}

  async validate(userId: string): Promise<boolean> {
    if (!userId) return true; // Let @IsOptional handle empty values

    try {
      const user = await this.userModel.findById(userId).exec();
      return !!user;
    } catch {
      return false;
    }
  }

  defaultMessage(args: ValidationArguments): string {
    return `User with ID "${args.value}" does not exist`;
  }
}

/**
 * Custom decorator to validate if a user ID exists in the database
 * @param validationOptions - Optional validation options
 * @returns PropertyDecorator
 * 
 * @example
 * ```typescript
 * @IsUserExists({ message: "The assigned user does not exist" })
 * assignedToId?: string;
 * 
 * @IsUserExists({ each: true, message: "One or more users do not exist" })
 * userIds?: string[];
 * ```
 */
export function IsUserExists(validationOptions?: ValidationOptions) {
  return function (object: object, propertyName: string) {
    registerDecorator({
      target: object.constructor,
      propertyName: propertyName,
      options: validationOptions,
      constraints: [],
      validator: IsUserExistsConstraint,
    });
  };
}
