import {
  Controller,
  Get,
  Query,
  Post,
  UseGuards,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiQuery,
  ApiResponse,
  ApiBearerAuth,
} from '@nestjs/swagger';
import { ApiLoggerService } from '../services/api-logger.service';
import { JwtAuthGuard } from '../../modules/auth/guards/jwt-auth.guard';

@ApiTags('Logging')
@Controller('logging')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class LoggingController {
  constructor(private readonly apiLoggerService: ApiLoggerService) {}

  @Get('stats')
  @ApiOperation({ summary: 'Get API request statistics' })
  @ApiQuery({
    name: 'date',
    description: 'Date in YYYY-MM-DD format (defaults to today)',
    required: false,
    example: '2024-01-15',
  })
  @ApiResponse({ status: 200, description: 'Statistics retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async getLogStats(@Query('date') date?: string) {
    return this.apiLoggerService.getLogStats(date);
  }

  @Post('cleanup')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Clean up old log files' })
  @ApiQuery({
    name: 'daysToKeep',
    description: 'Number of days to keep logs (default: 30)',
    required: false,
    type: Number,
    example: 30,
  })
  @ApiResponse({ status: 200, description: 'Cleanup completed successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized' })
  async cleanupLogs(@Query('daysToKeep') daysToKeep?: number) {
    const days = daysToKeep ? parseInt(daysToKeep.toString(), 10) : 30;
    await this.apiLoggerService.cleanupOldLogs(days);
    return { message: `Cleanup completed. Kept logs for the last ${days} days.` };
  }
}
