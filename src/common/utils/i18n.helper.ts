import {
  BadRequestException,
  ConflictException,
  ForbiddenException,
  NotFoundException,
  UnauthorizedException,
} from "@nestjs/common";

/**
 * Helper class for internationalized exceptions
 * This allows us to throw exceptions with i18n keys that will be translated by our exception filter
 */
export class I18nHelper {
  static badRequest(key: any): BadRequestException {
    return new BadRequestException(key);
  }

  static notFound(key: any): NotFoundException {
    return new NotFoundException(key);
  }

  static conflict(key: any): ConflictException {
    return new ConflictException(key);
  }

  static unauthorized(key: any): UnauthorizedException {
    return new UnauthorizedException(key);
  }

  static forbidden(key: any): ForbiddenException {
    return new ForbiddenException(key);
  }
}
