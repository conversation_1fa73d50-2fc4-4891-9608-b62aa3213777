import { Injectable, NestMiddleware, Logger } from "@nestjs/common";
import { Request, Response, NextFunction } from "express";

@Injectable()
export class RequestLoggingMiddleware implements NestMiddleware {
  private readonly logger = new Logger("HTTP");

  use(req: Request, res: Response, next: NextFunction): void {
    const startTime = Date.now();
    const { method, originalUrl, ip } = req;
    const userAgent = req.get("User-Agent") || "";

    // Get user info if available
    const userId = (req as any).user?.sub || (req as any).user?.id;
    const userInfo = userId ? ` [User: ${userId}]` : "";

    // Log the incoming request
    this.logger.log(
      `→ ${method} ${originalUrl} - ${ip} - ${userAgent}${userInfo}`
    );

    // Override the end method to log when response is sent
    const originalEnd = res.end.bind(res);
    res.end = function (chunk?: any, encoding?: any, cb?: any): Response {
      const responseTime = Date.now() - startTime;
      const { statusCode } = res;

      // Determine log level based on status code
      const logLevel = statusCode >= 400 ? "warn" : "log";
      const statusIcon = statusCode >= 400 ? "✗" : "✓";

      // Log the response
      const logger = new Logger("HTTP");
      logger[logLevel](
        `← ${statusIcon} ${method} ${originalUrl} ${statusCode} ${responseTime}ms${userInfo}`
      );

      // Call the original end method and return the result
      return originalEnd(chunk, encoding, cb);
    };

    next();
  }
}
