<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Reset Password</title>
  </head>
  <body
    style="
      margin: 0;
      padding: 0;
      background-color: transparent;
      font-family: Arial, sans-serif;
      display: flex;
      justify-content: center;
      align-items: center;
    "
  >
    <table
      width="900px"
      height="1024px"
      cellpadding="0"
      cellspacing="0"
      border="0"
      style="
        background: linear-gradient(
          to bottom,
          #1e2b64 0%,
          #1e2b64 40%,
          #f1f4f9 40%,
          #f1f4f9 100%
        );
        padding: 40px 60px;
      "
    >
      <tr>
        <td align="center">
          <table
            width="100%"
            height="900px"
            cellpadding="0"
            cellspacing="0"
            border="0"
            style="margin: 0 auto"
          >
            <tr>
              <td
                align="center"
                bgcolor="#1e2b64"
                style="
                  color: #ffffff;
                  font-size: 24px;
                  font-weight: bold;
                  padding-bottom: 20px;
                "
              >
                LOGO
              </td>
            </tr>

            <tr>
              <td>
                <table
                  width="100%"
                  cellpadding="0"
                  cellspacing="0"
                  border="0"
                  bgcolor="#ffffff"
                  style="
                    border-radius: 12px;
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
                    padding: 36px;
                  "
                >
                  <tr>
                    <td align="center" style="padding-bottom: 20px">
                      <img
                        src="https://res.cloudinary.com/dxshdgx8o/image/upload/v1745307224/Group_osttgs.png"
                        alt="Email Verify"
                        width="170"
                        height="auto"
                        style="
                          display: block;
                          margin: 0 auto;
                          padding-bottom: 20px;
                        "
                      />
                    </td>
                  </tr>
                  <tr>
                    <td
                      align="center"
                      style="
                        font-size: 26px;
                        font-weight: 600;
                        color: #333333;
                        padding-bottom: 20px;
                      "
                    >
                      Verify Your Email
                    </td>
                  </tr>

                  <tr>
                    <td
                      style="
                        font-size: 16px;
                        color: #555555;
                        padding-bottom: 20px;
                        line-height: 1.8;
                      "
                    >
                      Hello,
                      <br />
                      OnlyChat received a request to use
                      <strong>{{email}}</strong>
                      as a verify email for OnlyChat account. Use this code to
                      finish setting up:
                    </td>
                  </tr>

                  <tr>
                    <td
                      align="center"
                      style="
                        font-size: 32px;
                        font-weight: bold;
                        color: #333333;
                        padding: 20px 0;
                      "
                    >
                      {{code}}
                    </td>
                  </tr>

                  <tr>
                    <td align="left" style="font-size: 14px; color: #555555">
                      This code will expire in 24 hours.
                    </td>
                  </tr>
                  <tr>
                    <td align="left" style="font-size: 14px; color: #555555">
                      <table
                        cellpadding="0"
                        cellspacing="0"
                        border="0"
                        style="font-size: 14px; color: #555555"
                      >
                        <tr style="line-height: 2.0">
                          <td width="100">IP</td>
                          <td>: {{ip}}</td>
                        </tr>
                        <tr style="line-height: 2.0">
                          <td>Country</td>
                          <td>: {{country}}</td>
                        </tr>
                        <tr style="line-height: 2.0">
                          <td>Device</td>
                          <td>: {{browser}} on {{os}}</td>
                        </tr>
                      </table>
                      If you don’t recognize, you can safely ignore this email.
                    </td>
                  </tr>

                  <tr>
                    <td align="left" style="padding: 20px 0">
                      <table
                        width="60"
                        border="0"
                        cellpadding="0"
                        cellspacing="0"
                        style="border-top: 1px solid #dddddd"
                      >
                        <tr>
                          <td height="1"></td>
                        </tr>
                      </table>
                    </td>
                  </tr>

                  <tr style="line-height: 1.8">
                    <td align="left" style="font-size: 14px; color: #555555">
                      Sincerely,<br />
                      The OnlyChat team.
                    </td>
                  </tr>
                </table>
              </td>
            </tr>

            <tr>
              <td
                align="center"
                style="font-size: 14px; color: #333333; padding: 20px 0"
              >
                Please do not reply to this email.
              </td>
            </tr>
          </table>
        </td>
      </tr>
    </table>
  </body>
</html>