import { Modu<PERSON> } from "@nestjs/common";
import { MailerModule } from "@nestjs-modules/mailer";
import { ConfigModule, ConfigService } from "@nestjs/config";
import { MailService } from "./mail.service";
import { HandlebarsAdapter } from "@nestjs-modules/mailer/dist/adapters/handlebars.adapter";
import { join } from "path";

@Module({
  imports: [
    MailerModule.forRootAsync({
      inject: [ConfigService],
      useFactory: (config: ConfigService) => ({
        transport: {
          host: config.get("mail.transport.host", "smtp.gmail.com"),
          port: config.get("mail.transport.port", 587),
          secure: config.get("mail.transport.secure", false),
          auth: {
            user: config.get("mail.transport.auth.user", "<EMAIL>"),
            pass: config.get("mail.transport.auth.pass", "bGYu6DgUP9eQY463"),
          },
        },
        defaults: {
          from: config.get(
            "mail.defaults.from",
            "OnlyChat Supporter <<EMAIL>>"
          ),
        },
        template: {
          dir: join(__dirname, "..", "mail/templates"),
          adapter: new HandlebarsAdapter(),
          options: {
            strict: true,
          },
        },
      }),
    }),
    ConfigModule,
  ],
  providers: [MailService],
  exports: [MailService],
})
export class MailModule {}
