import { Injectable, Logger } from "@nestjs/common";
import { MailerService } from "@nestjs-modules/mailer";
import { ConfigService } from "@nestjs/config";
import { AnalyticsData } from "src/common/services/analytics.service";

@Injectable()
export class MailService {
  private readonly logger = new Logger(MailService.name);
  private readonly frontendURL: string;

  constructor(
    private readonly mailerService: MailerService,
    private configService: ConfigService
  ) {
    const fontendURL = configService.get<string>("FRONTEND_URL");
    if (!fontendURL) {
      throw new Error("FRONTEND_URL is not defined in the environment");
    }
    this.frontendURL = fontendURL;
  }

  /**
   * Send verification code email
   */
  async sendVerificationCode(
    email: string,
    code: string,
    analyticsData?: AnalyticsData
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: "Verify Your Email - OnlyChat",
        template: "email-verify",
        context: {
          email: email,
          code: code,
          ip: analyticsData?.ip,
          country: analyticsData?.countryName,
          browser: analyticsData?.browser,
          os: analyticsData?.os,
        },
      });

      this.logger.log(
        `Verification email sent to ${email} using HTML template`
      );
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${email}`,
        error instanceof Error ? error.stack : String(error)
      );
      throw new Error("Failed to send verification email");
    }
  }

  /**
   * Send welcome email after registration
   */
  async sendWelcomeEmail(email: string, name: string): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: "Welcome to OnlyChat!",
        template: "welcome",
        context: {
          name: name,
          learnMoreUrl: `${this.getDashboardUrl()}/learn-more`,
          dashboardUrl: this.getDashboardUrl(),
          contactUrl: `${this.getDashboardUrl()}/contact`,
        },
      });

      this.logger.log(`Welcome email sent to ${email} using HTML template`);
    } catch (error) {
      this.logger.error(
        `Failed to send welcome email to ${email}`,
        error instanceof Error ? error.stack : String(error)
      );
      throw new Error("Failed to send welcome email");
    }
  }

  private getDashboardUrl(): string {
    return this.frontendURL + "/dashboard";
  }

  private getResetPasswordUrl(token: string): string {
    return this.frontendURL + "/auth/forgot-password?token=" + token;
  }

  private getInvitationAcceptanceUrl(token: string): string {
    return `${this.frontendURL}/auth/accept-invitation?token=${token}`;
  }

  /**
   * Send workspace invitation email
   */
  async sendInvitationEmail(
    email: string,
    workspaceName: string,
    inviterName: string,
    invitationToken: string
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: `Invitation to join ${workspaceName} on OnlyChat`,
        template: "invite-developer",
        context: {
          ownerName: inviterName,
          workspaceName: workspaceName,
          acceptUrl: this.getInvitationAcceptanceUrl(invitationToken),
        },
      });

      this.logger.log(`Invitation email sent to ${email} using HTML template`);
    } catch (error) {
      this.logger.error(
        `Failed to send invitation email to ${email}`,
        error instanceof Error ? error.stack : String(error)
      );
      throw new Error("Failed to send invitation email");
    }
  }

  /**
   * Send reset password email
   */
  async sendResetPassword(
    email: string,
    name: string,
    token: string,
    analyticsData?: AnalyticsData
  ): Promise<void> {
    try {
      await this.mailerService.sendMail({
        to: email,
        subject: "Reset Password - OnlyChat",
        template: "reset-password",
        context: {
          name: name,
          resetUrl: this.getResetPasswordUrl(token),
          ip: analyticsData?.ip,
          country: analyticsData?.countryName,
          browser: analyticsData?.browser,
          os: analyticsData?.os,
        },
      });
      this.logger.log(
        `Reset password email sent to ${email} using HTML template`
      );
    } catch (error) {
      this.logger.error(
        `Failed to send verification email to ${email}`,
        error instanceof Error ? error.stack : String(error)
      );
      throw new Error("Failed to send reset password");
    }
  }
}
