# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/only-chat
MONGODB_USER=
MONGODB_PASSWORD=

# Application Configuration
PORT=3000
NODE_ENV=development
JWT_SECRET=pqawoiqtcnmjxwtaalyo
JWT_EXPIRATION=30d

# Google OAuth Configuration
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
GOOGLE_CALLBACK_URL=http://localhost:3000/api/auth/google/redirect

# Frontend URL
FRONTEND_URL=http://localhost:3000

# CORS Configuration
CORS_ORIGIN=["https://sombes.com", "http://localhost:3000", "http://localhost:52800"]

# Redis Configuration
REDIS_URL=redis://localhost:6379
USE_KAFKA=false
KAFKA_BROKER=localhost:9092

# Logging Configuration
LOG_DETAILED=false
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false
LOG_HEADERS=false
LOG_TO_FILE=true
LOG_TO_CONSOLE=true
LOG_DIRECTORY=./logs

# GraphQL Logging Configuration
LOG_GRAPHQL_QUERIES=true
LOG_GRAPHQL_VARIABLES=false
LOG_GRAPHQL_RESPONSES=false
LOG_GRAPHQL_ERRORS=true
LOG_GRAPHQL_RESOLVER_DETAILS=false