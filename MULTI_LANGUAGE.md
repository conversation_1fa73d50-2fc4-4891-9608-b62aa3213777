# Multi-Language Support

This API supports multi-language error messages. You can specify your preferred language, and the API will return error messages in that language.

## Supported Languages

Currently, the following languages are supported:

- English (en) - Default
- Vietnamese (vi)

## How to Specify Your Language Preference

You can specify your language preference in one of the following ways:

### 1. Query Parameter

Add the `lang` query parameter to your API requests:

```
GET /api/users/123?lang=vi
```

### 2. Accept-Language Header

Set the `Accept-Language` header in your HTTP requests:

```
Accept-Language: vi
```

## Examples

### Example 1: English Error Messages

**Request:**

```
GET /api/users/nonexistent-id
```

**Response:**

```json
{
  "statusCode": 404,
  "message": "User not found",
  "error": "Not Found",
  "timestamp": "2023-04-17T12:34:56.789Z",
  "path": "/api/users/nonexistent-id"
}
```

### Example 2: Vietnamese Error Messages

**Request:**

```
GET /api/users/nonexistent-id?lang=vi
```

**Response:**

```json
{
  "statusCode": 404,
  "message": "Không tìm thấy người dùng",
  "error": "Not Found",
  "timestamp": "2023-04-17T12:34:56.789Z",
  "path": "/api/users/nonexistent-id"
}
```

## Adding New Translations

To add translations for a new language:

1. Create a new directory under `src/i18n` with the ISO language code (e.g., `src/i18n/fr` for French)
2. Copy the JSON files from an existing language directory
3. Translate the values in the JSON files
4. The new language will be automatically available

## Default Language

If no language preference is specified, or if a requested language is not supported, the API will fall back to English (en).
