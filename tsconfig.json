{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "noImplicitAny": false, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false, "paths": {"@app/users": ["src/modules/users"], "@app/users/*": ["src/modules/users/*"], "@app/shared": ["src/shared"], "@app/shared/*": ["src/shared/*"], "@app/config": ["src/config"], "@app/database": ["src/database"]}}}