# Only Chat API Documentation

Welcome to the Only Chat API documentation. This directory contains detailed information about different aspects of the Only Chat API.

## Available Documentation

### REST API Documentation

The REST API documentation is available through Swagger UI at [/api/docs](../api/docs) when the server is running.

### WebSocket Chat API

For real-time chat functionality using WebSockets, please see the [WebSocket Chat API Documentation](WEBSOCKET_CHAT_API.md).

### Other Documentation

- [Login API Documentation](LOGIN_API.md) - Details about the authentication flow

## Using the API

The Only Chat backend provides two main types of APIs:

1. **REST API** - For typical CRUD operations and data management
2. **WebSocket API** - For real-time chat functionality

Make sure to check the appropriate documentation based on what you're trying to implement.

## Getting Started

1. Start by authenticating using the Login API
2. Use the REST endpoints to manage workspaces, users, etc.
3. For chat functionality, connect to the WebSocket API using Socket.IO
