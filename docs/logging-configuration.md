# API Request Logging Configuration

This document describes how to configure API request logging in the application.

## Environment Variables

Add these environment variables to your `.env` file to configure logging behavior:

### Basic Logging Configuration

```env
# Enable/disable detailed logging (default: false)
LOG_DETAILED=true

# Enable/disable request body logging (default: false)
LOG_REQUEST_BODY=true

# Enable/disable response body logging (default: false)
LOG_RESPONSE_BODY=false

# Enable/disable headers logging (default: false)
LOG_HEADERS=false

# Enable/disable file logging (default: false)
LOG_TO_FILE=true

# Enable/disable console logging (default: true)
LOG_TO_CONSOLE=true

# Log directory path (default: ./logs)
LOG_DIRECTORY=./logs

# GraphQL Logging Configuration
LOG_GRAPHQL_QUERIES=true
LOG_GRAPHQL_VARIABLES=false
LOG_GRAPHQL_RESPONSES=false
LOG_GRAPHQL_ERRORS=true
LOG_GRAPHQL_RESOLVER_DETAILS=false
```

### Security Considerations

The logging system automatically sanitizes sensitive information:

- **Headers**: `authorization`, `cookie`, `x-api-key`, `x-auth-token` are redacted
- **Request Body**: `password`, `token`, `secret`, `key`, `auth` fields are redacted
- **Response Body**: Large responses (>1000 chars) are truncated

## Logging Levels

The system provides different levels of logging:

1. **Basic Logging**: Method, URL, status code, response time, user info
2. **Detailed Logging**: Includes request/response bodies, headers, query params
3. **File Logging**: Stores logs in JSON format for analysis
4. **Console Logging**: Real-time logging to console

## GraphQL Logging

GraphQL requests are handled separately from REST API requests:

### GraphQL Configuration Options

- **LOG_GRAPHQL_QUERIES**: Log GraphQL query/mutation/subscription text
- **LOG_GRAPHQL_VARIABLES**: Log GraphQL operation variables
- **LOG_GRAPHQL_RESPONSES**: Log GraphQL response data
- **LOG_GRAPHQL_ERRORS**: Log GraphQL errors and validation issues
- **LOG_GRAPHQL_RESOLVER_DETAILS**: Log individual resolver execution details

### GraphQL Log Format

GraphQL operations are logged with the format:

```
GraphQL query getUserProfile 200 45ms [User: user123] [Workspace: ws456]
GraphQL mutation createMessage 201 120ms [User: user123]
```

### GraphQL Error Logging

GraphQL errors include detailed information:

```
GraphQL query getMessages ERROR: Cannot return null for non-nullable field [User: user123]
```

## API Endpoints

### Get Logging Statistics

```http
GET /logging/stats?date=2024-01-15
Authorization: Bearer <token>
```

Response:

```json
{
  "totalRequests": 1250,
  "successfulRequests": 1180,
  "errorRequests": 70,
  "averageResponseTime": 145.5,
  "methodStats": {
    "GET": 800,
    "POST": 300,
    "PUT": 100,
    "DELETE": 50
  },
  "statusCodeStats": {
    "2xx": 1180,
    "4xx": 60,
    "5xx": 10
  },
  "topEndpoints": {
    "GET /api/chat/messages": 250,
    "POST /api/auth/login": 100,
    "GET /api/helpdesk/categories": 80
  }
}
```

### Cleanup Old Logs

```http
POST /logging/cleanup?daysToKeep=30
Authorization: Bearer <token>
```

## Log File Format

When file logging is enabled, logs are stored in JSON format:

```json
{
  "timestamp": "2024-01-15T10:30:45.123Z",
  "method": "POST",
  "url": "/api/chat/messages",
  "statusCode": 201,
  "responseTime": 125,
  "ip": "*************",
  "userAgent": "Mozilla/5.0...",
  "userId": "user123",
  "workspaceId": "workspace456",
  "requestBody": {
    "message": "Hello world",
    "channelId": "channel789"
  },
  "query": {},
  "params": {}
}
```

## Performance Considerations

- **Request Body Logging**: Only enable for debugging, can impact performance
- **Response Body Logging**: Disabled by default, can consume significant storage
- **File Logging**: Asynchronous, minimal performance impact
- **Log Cleanup**: Automatically removes old files to prevent disk space issues

## Example Production Configuration

For production environments, use minimal logging:

```env
LOG_DETAILED=false
LOG_REQUEST_BODY=false
LOG_RESPONSE_BODY=false
LOG_HEADERS=false
LOG_TO_FILE=true
LOG_TO_CONSOLE=true
LOG_DIRECTORY=/var/log/only-chat
```

For development environments, use detailed logging:

```env
LOG_DETAILED=true
LOG_REQUEST_BODY=true
LOG_RESPONSE_BODY=true
LOG_HEADERS=true
LOG_TO_FILE=true
LOG_TO_CONSOLE=true
LOG_DIRECTORY=./logs
```
