# WebSocket Chat API Documentation

This document provides detailed information on how to integrate with the Only Chat WebSocket API for real-time chat functionality.

## Connection

The WebSocket server uses Socket.IO and is available at the `/chat` namespace.

### Connection URL

```
ws://your-server-url/chat
```

or for secure connections:

```
wss://your-server-url/chat
```

### Authentication

The chat system supports two types of authentication:

#### 1. Agent/User Authentication

Agents (support staff, admins) should connect with a JWT token:

```javascript
const socket = io("/chat", {
  auth: {
    token: "your-jwt-token", // The same JWT token used for REST API authentication
  },
});
```

#### 2. Guest Authentication

Guests (website visitors) should connect with a guest identifier:

```javascript
const socket = io("/chat", {
  auth: {
    guestIdentifier: "<EMAIL>", // Email or unique identifier
  },
});
```

## Events

### Events emitted by client

#### `send_agent_message`

Sends a message as an agent to a specific conversation.

```javascript
socket.emit(
  "send_agent_message",
  {
    conversationId: "conversation-id",
    message: {
      content: "Hello, how can I help you today?",
      type: "text", // Optional, defaults to "text". Other options: "file", "image", "system"
      metadata: {}, // Optional, additional data for the message
    },
  },
  (response) => {
    // Handle response
    if (response.success) {
      console.log("Message sent, ID:", response.messageId);
    }
  }
);
```

#### `send_guest_message`

Sends a message as a guest to a specific conversation.

```javascript
socket.emit(
  "send_guest_message",
  {
    conversationId: "conversation-id",
    message: {
      content: "I have a question about your product",
      type: "text", // Optional, defaults to "text"
      metadata: {}, // Optional, additional data
    },
  },
  (response) => {
    // Handle response
    if (response.success) {
      console.log("Message sent, ID:", response.messageId);
    }
  }
);
```

#### `join_conversation`

Joins a specific conversation room to receive messages from it.

```javascript
socket.emit(
  "join_conversation",
  {
    conversationId: "conversation-id",
  },
  (response) => {
    if (response.success) {
      console.log("Joined conversation");
    }
  }
);
```

#### `leave_conversation`

Leaves a conversation room.

```javascript
socket.emit(
  "leave_conversation",
  {
    conversationId: "conversation-id",
  },
  (response) => {
    if (response.success) {
      console.log("Left conversation");
    }
  }
);
```

### Events emitted by server

#### `message`

Emitted when a new message is sent to a conversation you're subscribed to.

```javascript
socket.on("message", (data) => {
  console.log("New message in conversation", data.conversationId);
  console.log("Message:", data);

  // Message structure
  // {
  //   id: "message-id",
  //   content: "Hello!",
  //   sender: "agent", // or "guest" or "system"
  //   type: "text",
  //   status: "sent",
  //   metadata: {},
  //   createdAt: "2025-04-19T10:00:00.000Z",
  //   userId: "user-id", // if sender is agent
  //   conversationId: "conversation-id"
  // }
});
```

## Error Handling

Socket.IO will emit an `error` event when there's an exception.

```javascript
socket.on("error", (error) => {
  console.error("Socket error:", error);
});

socket.on("connect_error", (error) => {
  console.error("Connection error:", error);
});
```

## Integration Example

Here's a complete example of connecting to the chat system as a guest and sending a message:

```javascript
import { io } from "socket.io-client";

// Connect as guest
const socket = io("https://your-server-url/chat", {
  auth: {
    guestIdentifier: "<EMAIL>",
  },
});

// Connection status handling
socket.on("connect", () => {
  console.log("Connected to chat server");
});

socket.on("disconnect", () => {
  console.log("Disconnected from chat server");
});

// Listen for messages
socket.on("message", (data) => {
  console.log("New message:", data);
});

// Join a conversation (after starting one via REST API)
function joinConversation(conversationId) {
  socket.emit("join_conversation", { conversationId }, (response) => {
    if (response.success) {
      console.log("Joined conversation");
    } else {
      console.error("Failed to join conversation");
    }
  });
}

// Send a message
function sendMessage(conversationId, content) {
  socket.emit(
    "send_guest_message",
    {
      conversationId,
      message: {
        content,
        type: "text",
      },
    },
    (response) => {
      if (response.success) {
        console.log("Message sent successfully");
      } else {
        console.error("Failed to send message");
      }
    }
  );
}

// Error handling
socket.on("error", (error) => {
  console.error("Socket error:", error);
});
```

## Related REST APIs

Note that some operations require using the REST API first:

- To start a new conversation as a guest, use the `POST /api/guest-chat/conversations` endpoint
- To retrieve conversation history, use the appropriate REST endpoints based on user type

See the Swagger documentation at `/api/docs` for details on these REST endpoints.
