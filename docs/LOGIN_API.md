# Login API Documentation

The login API allows users to authenticate with the application using their email and password. Upon successful authentication, the API returns a JWT token that can be used for accessing protected resources.

## Endpoint

```
POST /api/auth/login
```

## Request Body

| Field    | Type   | Description          |
| -------- | ------ | -------------------- |
| email    | string | User's email address |
| password | string | User's password      |

### Example Request

```json
{
  "email": "<EMAIL>",
  "password": "Password123!"
}
```

## Responses

### Success Response (200 OK)

```json
{
  "message": "Login successful",
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "userId": "5f9d5a4b8c6d3a2e1c0b9a8d"
}
```

### Error Responses

#### Invalid Credentials (401 Unauthorized)

```json
{
  "statusCode": 401,
  "message": "Invalid email or password",
  "error": "Unauthorized",
  "timestamp": "2023-04-17T12:34:56.789Z",
  "path": "/api/auth/login"
}
```

#### User Not Verified (401 Unauthorized)

```json
{
  "statusCode": 401,
  "message": "User account is not verified",
  "error": "Unauthorized",
  "timestamp": "2023-04-17T12:34:56.789Z",
  "path": "/api/auth/login"
}
```

## Usage with Multi-Language Support

The login API supports multiple languages for error messages. You can specify your preferred language using the `lang` query parameter or the `Accept-Language` header.

### Example with Query Parameter

```
POST /api/auth/login?lang=vi
```

### Example with Header

```
Accept-Language: vi
```

## Using the JWT Token

After successful login, use the returned JWT token in the Authorization header for subsequent requests to protected endpoints:

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```
