# NGINX Configuration Documentation

## Overview

This document outlines the NGINX configuration used for the OnlyChat API server. The configuration supports both direct connections and connections through Cloudflare as a CDN/proxy.

## Server Configuration

The main NGINX configuration file is located at:

```
/etc/nginx/sites-available/api.sombes.com
```

And is symlinked to:

```
/etc/nginx/sites-enabled/api.sombes.com
```

## Configuration Details

### Basic Server Setup

```nginx
server {
    server_name api.sombes.com www.api.sombes.com;

    # SSL configuration managed by Certbot
    listen 443 ssl;
    ssl_certificate /etc/letsencrypt/live/api.sombes.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.sombes.com/privkey.pem;
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;
}
```

### API Endpoints

#### With Cloudflare

```nginx
location / {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # Cloudflare specific headers
    proxy_set_header CF-Connecting-IP $http_cf_connecting_ip;
    proxy_set_header CF-IPCountry $http_cf_ipcountry;
    proxy_set_header CF-Ray $http_cf_ray;

    proxy_cache off;
    proxy_no_cache 1;
    proxy_cache_bypass 1;
}
```

#### Without Cloudflare

```nginx
location / {
    proxy_pass http://localhost:3000;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    proxy_cache off;
    proxy_no_cache 1;
    proxy_cache_bypass 1;
}
```

### WebSocket Configuration

#### With Cloudflare

```nginx
location /socket.io/ {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;

    # Important headers
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;

    # Cloudflare specific headers
    proxy_set_header CF-Connecting-IP $http_cf_connecting_ip;
    proxy_set_header CF-IPCountry $http_cf_ipcountry;
    proxy_set_header CF-Ray $http_cf_ray;
}
```

#### Without Cloudflare

```nginx
location /socket.io/ {
    proxy_pass http://localhost:3000;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "upgrade";
    proxy_set_header Host $host;

    # Important headers
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
}
```

## Important Headers Explanation

| Header              | Purpose                                                                     | Required for    |
| ------------------- | --------------------------------------------------------------------------- | --------------- |
| `Host`              | Passes the original host requested by the client                            | All setups      |
| `X-Real-IP`         | Sets the client's real IP address                                           | All setups      |
| `X-Forwarded-For`   | Appends the client IP to any existing X-Forwarded-For chain                 | All setups      |
| `X-Forwarded-Proto` | Indicates whether the original request used HTTPS or HTTP                   | All setups      |
| `CF-Connecting-IP`  | Cloudflare-specific header containing the original client IP                | Cloudflare only |
| `CF-IPCountry`      | Two-letter country code of the client's country                             | Cloudflare only |
| `CF-Ray`            | Unique identifier for the request through Cloudflare (useful for debugging) | Cloudflare only |

## WebSocket-specific Headers

| Header       | Purpose                                    | Required for |
| ------------ | ------------------------------------------ | ------------ |
| `Upgrade`    | Required for WebSocket protocol upgrade    | All setups   |
| `Connection` | Set to "upgrade" for WebSocket connections | All setups   |

## Cache Configuration

The configuration disables caching for API responses with these directives:

```
proxy_cache off;
proxy_no_cache 1;
proxy_cache_bypass 1;
```

## SSL Configuration

SSL is managed by Certbot with automatic renewal. The certificates are stored in:

```
/etc/letsencrypt/live/api.sombes.com/
```

## Maintenance Commands

### Test NGINX Configuration

```bash
sudo nginx -t
```

### Reload NGINX Configuration

```bash
sudo systemctl reload nginx
```

### Restart NGINX

```bash
sudo systemctl restart nginx
```

### View NGINX Logs

```bash
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## Backend Code Considerations

When working with or without Cloudflare, the backend code should be adjusted to handle IP detection properly:

```typescript
private getClientIp(req: Request): string {
  // For Cloudflare setups
  const cfConnectingIp = req.headers["cf-connecting-ip"] as string;
  if (cfConnectingIp) {
    return cfConnectingIp;
  }

  // For non-Cloudflare setups
  const xForwardedFor = req.headers["x-forwarded-for"] as string;
  if (xForwardedFor) {
    const forwardedIps = xForwardedFor.split(",").map((ip) => ip.trim());
    return forwardedIps[0]; // First IP is usually the client
  }

  const xRealIp = req.headers["x-real-ip"] as string;
  if (xRealIp) {
    return xRealIp;
  }

  // Fallback to direct connection IP
  return req.connection?.remoteAddress || "unknown";
}
```
