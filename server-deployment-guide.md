# Server Deployment Guide for Only Chat Application

This guide provides detailed instructions for deploying the Only Chat application directly on a server without using Docker, ensuring scalability to support millions of concurrent users.

## Table of Contents

- [System Requirements](#system-requirements)
- [Setting Up Application Servers](#setting-up-application-servers)
- [Setting Up MongoDB](#setting-up-mongodb)
- [Setting Up Redis](#setting-up-redis)
- [Setting Up NGINX Load Balancer](#setting-up-nginx-load-balancer)
- [Running Multiple Application Instances](#running-multiple-application-instances)
- [Monitoring and Maintenance](#monitoring-and-maintenance)
- [Scaling Up](#scaling-up)
- [Security Considerations](#security-considerations)
- [Backup Strategies](#backup-strategies)
- [Troubleshooting](#troubleshooting)

## System Requirements

For a scalable deployment capable of handling millions of concurrent users, we recommend:

### Application Servers

- 4+ CPU cores per server
- 16+ GB RAM per server
- 40+ GB SSD storage
- Ubuntu 20.04 LTS or newer

### Database Server (MongoDB)

- 8+ CPU cores
- 32+ GB RAM
- 100+ GB SSD storage
- RAID configuration for data safety

### Redis Server

- 4+ CPU cores
- 16+ GB RAM
- 20+ GB SSD storage

### Load Balancer

- 4+ CPU cores
- 8+ GB RAM
- 20+ GB SSD storage

## Setting Up Application Servers

### 1. Install Node.js

```bash
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
```

Verify installation:

```bash
node --version
npm --version
```

### 2. Clone the Application

```bash
mkdir -p /opt/applications
cd /opt/applications
git clone https://github.com/yourusername/only-chat-be.git
cd only-chat-be
```

### 3. Install Dependencies

```bash
npm install
```

### 4. Build the Application

```bash
npm run build
```

### 5. Create Environment Configuration

Create a `.env` file:

```bash
nano .env
```

Add the following configuration (adjust values as needed):

```
PORT=3000
NODE_ENV=production

# MongoDB Connection
MONGODB_URI=*************************************************************************

# Redis Connection
REDIS_URL=redis://default:your_secure_redis_password@redis-server:6379

# JWT Configuration
JWT_SECRET=your_very_secure_jwt_secret_key
JWT_EXPIRATION=1d
JWT_REFRESH_EXPIRATION=7d

# CORS
CORS_ORIGIN=https://your-frontend-domain.com

# Email Service (if applicable)
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_USER=your_email_user
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>

# Web Socket Configuration
WS_PATH=/socket.io
```

## Setting Up MongoDB

### 1. Install MongoDB

```bash
wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | sudo apt-key add -
echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu focal/mongodb-org/5.0 multiverse" | sudo tee /etc/apt/sources.list.d/mongodb-org-5.0.list
sudo apt-get update
sudo apt-get install -y mongodb-org
```

### 2. Configure MongoDB for Production

Edit the MongoDB configuration file:

```bash
sudo nano /etc/mongod.conf
```

Update the following sections:

```yaml
# network interfaces
net:
  port: 27017
  bindIp: 127.0.0.1,mongodb-server-ip # Add your server's IP

security:
  authorization: enabled # Enable authentication

# replication setup for high availability
replication:
  replSetName: rs0
```

### 3. Set Up Authentication

Start MongoDB:

```bash
sudo systemctl start mongod
sudo systemctl enable mongod
```

Connect to MongoDB and create an admin user:

```bash
mongo
```

```javascript
use admin
db.createUser({
  user: "admin",
  pwd: "secure_admin_password",
  roles: [ { role: "userAdminAnyDatabase", db: "admin" }, "readWriteAnyDatabase" ]
})
exit
```

Create the application database and user:

```bash
mongo -u admin -p secure_admin_password --authenticationDatabase admin
```

```javascript
use onlychat
db.createUser({
  user: "chat_app_user",
  pwd: "secure_app_password",
  roles: [ { role: "readWrite", db: "onlychat" } ]
})
exit
```

### 4. Set Up Replication (for High Availability)

For a production environment, set up a replica set by following MongoDB's official documentation.

## Setting Up Redis

### 1. Install Redis

```bash
sudo apt update
sudo apt install redis-server
```

### 2. Configure Redis for Production

Edit the Redis configuration file:

```bash
sudo nano /etc/redis/redis.conf
```

Make these changes:

```
bind 127.0.0.1 redis-server-ip  # Add your server's IP
requirepass your_secure_redis_password  # Set a password
maxmemory 8gb  # Adjust based on your server RAM
maxmemory-policy allkeys-lru  # Configure eviction policy
```

### 3. Enable and Start Redis

```bash
sudo systemctl restart redis-server
sudo systemctl enable redis-server
```

### 4. Verify Redis is Running

```bash
redis-cli
> AUTH your_secure_redis_password
> PING
PONG
```

## Setting Up NGINX Load Balancer

### 1. Install NGINX

```bash
sudo apt update
sudo apt install nginx
```

### 2. Configure NGINX for Load Balancing

Create a configuration file:

```bash
sudo nano /etc/nginx/sites-available/chat-app
```

Add the following configuration:

```nginx
upstream chat_backend {
    # List all your application instances
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
    server 127.0.0.1:3003;
    # Add more servers as needed

    # Use IP hash for session persistence
    ip_hash;
}

server {
    listen 80;
    server_name your-domain.com;

    # Redirect HTTP to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

server {
    listen 443 ssl;
    server_name your-domain.com;

    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers HIGH:!aNULL:!MD5;

    # API endpoints
    location /api/ {
        proxy_pass http://chat_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://chat_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Static frontend files (if needed)
    location / {
        root /var/www/chat-frontend;
        try_files $uri $uri/ /index.html;
    }
}
```

### 3. Enable the NGINX Configuration

```bash
sudo ln -s /etc/nginx/sites-available/chat-app /etc/nginx/sites-enabled/
sudo nginx -t  # Test the configuration
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 4. Set Up SSL with Let's Encrypt

```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

## Running Multiple Application Instances

### 1. Install PM2 Process Manager

```bash
sudo npm install -g pm2
```

### 2. Create a PM2 Ecosystem File

```bash
cd /opt/applications/only-chat-be
nano ecosystem.config.js
```

Add the following configuration:

```javascript
module.exports = {
  apps: [
    {
      name: "chat-app-3001",
      script: "dist/main.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      env: {
        NODE_ENV: "production",
        PORT: 3001,
      },
    },
    {
      name: "chat-app-3002",
      script: "dist/main.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      env: {
        NODE_ENV: "production",
        PORT: 3002,
      },
    },
    {
      name: "chat-app-3003",
      script: "dist/main.js",
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      env: {
        NODE_ENV: "production",
        PORT: 3003,
      },
    },
    // Add more instances as needed
  ],
};
```

### 3. Start the Application Instances

```bash
pm2 start ecosystem.config.js
```

### 4. Set Up PM2 to Start on Boot

```bash
pm2 startup
# Run the command provided by the output
pm2 save
```

## Monitoring and Maintenance

### 1. Install Monitoring Tools

```bash
# Install Node.js monitoring
sudo npm install -g pm2-logrotate
pm2 install pm2-server-monit

# Install system monitoring
sudo apt install htop iotop nload
```

### 2. Set Up Log Rotation

```bash
sudo nano /etc/logrotate.d/chat-app
```

Add the following configuration:

```
/opt/applications/only-chat-be/logs/*.log {
    daily
    missingok
    rotate 14
    compress
    delaycompress
    notifempty
    create 0640 root root
}
```

### 3. Create Status Check Endpoint

Add a health check endpoint to your application if it doesn't already exist.

### 4. Set Up Uptime Monitoring

Consider using services like UptimeRobot, Pingdom, or New Relic for external monitoring.

## Scaling Up

### Vertical Scaling

- Increase resources on existing servers (CPU, RAM)
- Tune Node.js for performance:
  ```bash
  # In your PM2 ecosystem file or .env
  NODE_OPTIONS=--max-old-space-size=4096
  ```

### Horizontal Scaling

- Add more application instances on the same server
- Deploy across multiple servers with a load balancer
- Set up Redis for session store and caching across instances

### Database Scaling

- Set up MongoDB replica sets for redundancy
- Consider sharding for very large data sets
- Implement database indexing strategies

### Redis Scaling

- Set up Redis Cluster for high availability
- Implement Redis Sentinel for monitoring and failover

## Security Considerations

### 1. Configure Firewall

```bash
sudo apt install ufw
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw allow from trusted-ip-address to any port 27017  # MongoDB
sudo ufw allow from trusted-ip-address to any port 6379   # Redis
sudo ufw enable
```

### 2. Regular Updates

```bash
sudo apt update
sudo apt upgrade
```

## Backup Strategies

### 1. MongoDB Backups

```bash
# Create a backup script
nano /opt/scripts/mongodb-backup.sh
```

Add the following content:

```bash
#!/bin/bash
TIMESTAMP=$(date +"%Y%m%d%H%M%S")
BACKUP_DIR="/opt/backups/mongodb"
mkdir -p $BACKUP_DIR

# MongoDB backup
mongodump --uri="*************************************************************************" --out="$BACKUP_DIR/$TIMESTAMP"

# Compress the backup
tar -czf "$BACKUP_DIR/$TIMESTAMP.tar.gz" -C "$BACKUP_DIR" "$TIMESTAMP"
rm -rf "$BACKUP_DIR/$TIMESTAMP"

# Delete backups older than 30 days
find $BACKUP_DIR -type f -name "*.tar.gz" -mtime +30 -delete
```

Make the script executable:

```bash
chmod +x /opt/scripts/mongodb-backup.sh
```

### 2. Set Up a Cron Job

```bash
crontab -e
```

Add the following line to run the backup daily at 2 AM:

```
0 2 * * * /opt/scripts/mongodb-backup.sh
```

## Troubleshooting

### Common Issues and Solutions

1. **Application won't start**

   - Check the logs: `pm2 logs`
   - Verify environment variables: `cat .env`
   - Check Node.js version: `node --version`

2. **MongoDB connection issues**

   - Check MongoDB status: `sudo systemctl status mongod`
   - Verify MongoDB user: `mongo -u chat_app_user -p secure_app_password --authenticationDatabase onlychat`
   - Check network connectivity: `telnet mongodb-server 27017`

3. **Redis connection issues**

   - Check Redis status: `sudo systemctl status redis-server`
   - Verify Redis connectivity: `redis-cli -h redis-server -a your_secure_redis_password ping`

4. **NGINX issues**
   - Check NGINX configuration: `sudo nginx -t`
   - Check NGINX status: `sudo systemctl status nginx`
   - Review NGINX logs: `sudo tail -f /var/log/nginx/error.log`

### Viewing Logs

```bash
# Application logs
pm2 logs

# MongoDB logs
sudo tail -f /var/log/mongodb/mongod.log

# Redis logs
sudo tail -f /var/log/redis/redis-server.log

# NGINX logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

## Conclusion

This guide provides a comprehensive approach to deploying the Only Chat application directly on server infrastructure without Docker. By following these instructions, you can set up a scalable system capable of handling millions of concurrent users.

For more advanced scaling needs, consider distributing your application across multiple servers or regions, implementing a CDN for static assets, and setting up database read replicas.

Remember that proper monitoring and maintenance are key to ensuring your application remains performant and reliable as your user base grows.
