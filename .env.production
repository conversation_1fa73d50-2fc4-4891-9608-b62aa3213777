# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/only-chat

# Application Configuration
PORT=3000
NODE_ENV=production
JWT_SECRET=pqawoiqtcnmjxwtaalyo
JWT_EXPIRATION=30d

# Google OAuth Configuration
GOOGLE_CLIENT_ID=555958537715-cv7sprp1k727t2m5gq24k4h7qg3kvfg1.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_CALLBACK_URL=https://api.sombes.com/api/auth/google/redirect

# Frontend URL
FRONTEND_URL=https://sombes.com

# CORS Configuration
CORS_ORIGIN=*

# Redis Configuration
REDIS_URL=redis://localhost:6379
USE_KAFKA=false
KAFKA_BROKER=localhost:9092